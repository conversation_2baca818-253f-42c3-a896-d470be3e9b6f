# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# Batch Normalization

导师：余老师

# 目录

Batch Normalization 概念

PyTorch的Batch Normalization 1d/2d/3d 实现

# Batch Normalization

Batch Normalization

Batch Normalization: 批标准化

批：一批数据，通常为mini- batch

标准化：0均值，1方差

优点：

优点：1. 可以用更大学习率，加速模型收敛2. 可以不用精心设计权值初始化3. 可以不用dropout或较小的dropout4. 可以不用L2或者较小的weight decay5. 可以不用LRN(local response normalization)

《Batch Normalization: Accelerating Deep Network Training by Reducing Internal Covariate Shift》读取50篇AI必读经典前沿论文

# Batch Normalization

Batch Normalization

计算方式

affine transfrom 增强Capacity

Input: Values of  $x$  over a mini- batch:  $\mathcal{B} = \{x_{1\ldots m}\}$  Parameters to be learned:  $\gamma ,\beta$  Output:  $\{y_{i} = \mathrm{BN}_{\gamma ,\beta}(x_{i})\}$ $\mu_{\mathcal{B}}\gets \frac{1}{m}\sum_{i = 1}^{m}x_{i}$  // mini- batch mean  $\sigma_{\mathcal{B}}^{2}\gets \frac{1}{m}\sum_{i = 1}^{m}(x_{i} - \mu_{\mathcal{B}})^{2}$  // mini- batch variance  $\widehat{x}_i\gets \frac{x_i - \mu_B}{\sqrt{\sigma_B^2 + \epsilon}}$  // normalize  $y_{i}\gets \gamma \widehat{x}_{i} + \beta \equiv \mathbf{BN}_{\gamma ,\beta}(x_{i})$  // scale and shift

Algorithm 1: Batch Normalizing Transform, applied to activation  $x$  over a mini- batch.

# Batch Normalization

Batch Normalization

# Internal Covariate Shift (ICS)

$$
\begin{array}{rl} & {\mathrm{H}_{11} = \sum_{i = 0}^{n}X_{i}*W_{1i}}\\ & {\mathsf{D}(\mathrm{H}_{11}) = \sum_{i = 0}^{n}D(X_{i})*D(W_{1i})}\\ & {\qquad = \mathsf{n}*(1*1)}\\ & {\qquad = \mathsf{n}}\\ & {\mathsf{std}(\mathrm{H}_{11}) = \sqrt{\mathsf{D}(\mathrm{H}_{11})} = \sqrt{n}}\\ & {\mathsf{D}(\mathrm{H}_{1}) = n*D(X)*D(W) = 1}\\ & {\qquad D(W) = \frac{1}{n}\Rightarrow \mathsf{std}(W) = \sqrt{\frac{1}{n}}} \end{array}
$$

![](images/fc4d6a883d0074b8d301cff1381ac45d4531fee3d31077fa3c9a7478a870e698.jpg)

X W1 H1 W2 H2 W3 out

# Batch Normalization

Batch Normalization

# BatchNorm

nn.BatchNorm1d nn.BatchNorm2d nn.BatchNorm3d

参数：

- num_features：一个样本特征数量（最重要）- eps：分母修正项- momentum：指数加权平均估计当前mean/var- affine：是否需要affine transform- track_running_stats：是训练状态，还是测试状态

__init__(self, num_features, eps=1e- 5, momentum=0.1, affine=True, track_running_stats=True)

# Batch Normalization

Batch Normalization

- nn.BatchNorm1d- nn.BatchNorm2d- nn.BatchNorm3d

主要属性：

- running_mean：均值- running_var：方差- weight：affine transform中的gamma- bias：affine transform中的beta

$\widehat{x}_i\gets \frac{x_i - \mu\beta}{\sqrt{\sigma_\beta^2 + \epsilon}}$ $y_{i}\leftarrow \gamma \widehat{x}_{i} + \beta \equiv \mathbf{BN}_{\gamma ,\beta}(x_{i})$

训练：均值和方差采用指数加权平均计算测试：当前统计值

running_mean = (1 - momentum) * pre_running_mean + momentum * mean_trunning_var = (1 - momentum) * pre_running_var + momentum * var_t关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# Batch Normalization

Batch Normalization

- nn.BatchNorm1d input = B*特征数*1d特征- nn.BatchNorm2d input = B*特征数*2d特征- nn.BatchNorm3d input = B*特征数*3d特征

![](images/e22a9b07cfb77e40f3403e3e2e7f1c5c3a10e69de4955ec5298a8a97d0bfba65.jpg)

![](images/9ffe24ee5a18d1e110c8f37b74e3e05f03f3e1ddf95c9b9491b25e239d56807a.jpg)

![](images/eada272a2c47a166649e226cac4980150fc95da9be8f062b1faec45a2fdc1efa.jpg)

![](images/68549b68ddf41d3388183952937b5d898f95c7473aa0f7b32cbdcc6c1e4f74a3.jpg)

![](images/b8835b75d831704d51cf6dffebfa596107f7db9f74517de2e638e9890d5f4eb4.jpg)

# 结语

在这次课程中，学习了Batch Normalization

在下次课程中，我们将会学习

其它 Normalization

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/9d49ae0bf6cec9ceb460efc2f01286b81b6d277eb2b22f2b18ee96796c89883c.jpg)  
公众号

![](images/cca5bbe750b242ed7d0bf919f9d3b88a9fa3fa91ea5bb3aa52daab9d20dabfd5.jpg)  
客服微信