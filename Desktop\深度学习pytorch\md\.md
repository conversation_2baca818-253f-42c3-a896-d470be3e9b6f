# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# 模型容器与AlexNet构建

导师：余老师

# 目录

1/网络层容器(Containers)  2/AlexNet构建

# 模型容器——Containers

Module Containers

# 容器

容器Containers

![](images/eabbfe3b25e077122742661a26a92ea9ae3fb4e1ea1d26b0a79bf81206292620.jpg)

# 容器之Sequential

nn.Sequential

# nn.Sequential 是 nn.module的容器，用于按顺序包装一组网络层

![](images/b3b6f803cea3519d68a3585a4478a82b0173557498ba1f59ad2bc3a4aaa46ce3.jpg)

# 容器之Sequential

nn.Sequential

nn.Sequential 是 nn.module的容器，用于按顺序包装一组网络层

- 顺序性：各网络层之间严格按照顺序构建- 自带forward()：自带的forward里，通过for循环依次执行前向传播运算

# 容器之ModuleList

nn.ModuleList

nn.ModuleList是nn.module的容器，用于包装一组网络层，以迭代方式调用网络层主要方法：

- append(): 在ModuleList后面添加网络层- extend(): 拼接两个ModuleList- insert(): 指定在ModuleList中位置插入网络层

# 容器之ModuleLDict

nn.ModuleDict

nn.ModuleDict是nn.module的容器，用于包装一组网络层，以索引方式调用网络层主要方法：

- clear(): 清空ModuleDict- items(): 返回可迭代的键值对(key-value pairs)- keys(): 返回字典的键(key)- values(): 返回字典的值(value)- pop(): 返回一对键值，并从字典中删除

# 容器总结Summary of Containers

- nn.Sequential：顺序性，各网络层之间严格按顺序执行，常用于block构建- nn.ModuleList：迭代性，常用于大量重复网构建，通过for循环实现重复构建- nn.ModuleDict：索引性，常用于可选择的网络层

# AlexNet构建

Create AlexNet

# AlexNet

AlexNet

AlexNet：2012年以高出第二名10多个百分点的准确率获得ImageNet分类任务冠军，开创了卷积神经网络的新时代

AlexNet特点如下：

1. 采用ReLU：替换饱和激活函数，减轻梯度消失  
2. 采用LRN(Local Response Normalization)：对数据归一化，减轻梯度消失  
3. Dropout：提高全连接层的鲁棒性，增加网络的泛化能力  
4. Data Augmentation：TenCrop，色彩修改

# AlexNet

AlexNet

![](images/2e0de9c16da94263f19a3ddb6b19ad82eb0a9a3df66e24705b8ebd4971ec30b8.jpg)

# features

# classifier

# 结语

在这次课程中，学习了三个模型容器Sequential, ModuleList, ModuleDict以及AlexNet的搭建在下次课程中，我们将会学习nn中网络层的具体使用

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/312cf7efd7309b45107207edff4ff45929fdb9e7495db9471e279a342286c263.jpg)  
公众号

![](images/52645d6994d2fb88da2b28858948b8015daa7b9f266125b6b282d02d7730837b.jpg)  
客服微信