# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# 正则化之Dropout

导师：余老师

# 目录

![](images/70cf8d800bfafb8ca4f350dab6406dece3ffe53b76fdefae1833e01b0564c471.jpg)

# Regularization

Regularization

Dropout: 随机失活

随机：dropout probability

失活：weight = 0

![](images/2599b90cbc7aeea8492782100c97e9430aa7d7a53261222b6959cc072004415a.jpg)

# Regularization

Regularization

# Dropout：随机失活

数据尺度变化：测试时，所有权重乘以1- drop_probdrop_prob = 0.3，1- drop_prob = 0.7

![](images/225191997a9011050e76c3977a71c4d3e3aa3f2eee632112fe5d4026f814bb71.jpg)  
(a) Standard Neural Net

![](images/ddd789a0303a50b1017fbf3493c0f92ffed3419e4d977b8fcd79ece5e3b0a7a8.jpg)  
(b) After applying dropout.

# Regularization

Regularization

nn.Dropout

功能：Dropout层

参数：

p：被舍弃概率，失活概率

实现细节：

训练时权重均乘以  $\frac{1}{1 - p}$ ，即除以1- p

torch.nn.Dropout(p=0.5, inplace=False)

# 结语

在这次课程中，学习了正则化的Dropout策略

在下次课程中，我们将会学习

Batch Normalization

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/ad58bb7c3e6c88885d1c7ce03949fd4b873fd4637b1e82bc2d484d2b4e4a01ab.jpg)  
公众号

![](images/0bc357a7fb8d3a5cc92b99be531d0079a3a7d7759a937c220acfdaad4b17ed9d.jpg)  
客服微信