# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# BN、LN、IN and GN

导师：余老师

# 目录

为什么要Normalization?

常见的Normalization——BN、LN、IN and GN

Normalization小结

# Normalization in DL

Normalization in DL

Why Normalization

Internal Covariate Shift (ICS): 数据尺度/分布异常，导致训练困难

$$
\begin{array}{rl} & {\mathrm{H}_{11} = \sum_{i = 0}^{n}X_{i}*W_{1i}}\\ & {\mathbf{D}(\mathrm{H}_{11}) = \sum_{i = 0}^{n}D(X_{i})*D(W_{1i})}\\ & {\qquad = \mathsf{n}*(1*1)}\\ & {\qquad = \mathsf{n}}\\ & {\mathsf{std}(\mathrm{H}_{11}) = \sqrt{\mathbf{D}(\mathrm{H}_{11})} = \sqrt{n}}\\ & {\mathbf{D}(\mathrm{H}_{1}) = n*D(X)*D(W)} \end{array}
$$

![](images/acd09a402300baa3f51465ae9cd5fcf6c386da7d3a5f02b1be165bc5e977eb65.jpg)

# Normalization in DL

Normalization in DL

# 常见的Normalization

常见的Normalization1. Batch Normalization (BN)3. Instance Normalization (IN)

2. Layer Normalization (LN)4. Group Normalization (GN)

# 同

# 异

$\begin{array}{l}\widehat{x}_i\leftarrow \frac{x_i - \mu_B}{\sqrt{\sigma_B^2 + \epsilon}}\\ y_i\leftarrow \gamma \widehat{x}_i + \beta \equiv \mathbf{N}_{\gamma ,\beta}(x_i) \end{array}$

均值和方差求取方式

# Normalization in DL

Normalization in DL

1. Layer Normalization

起因：BN不适用于变长的网络，如RNN

思路：逐层计算均值和方差

注意事项：

注意事项：1. 不再有running_mean和running_var2. gamma和beta为逐元素的

![](images/cc8c304c51507bea4729829b6711a43695e4086de01b0995e7e17dcb68dcd212.jpg)

# Normalization in DL

Normalization in DL

# nn.LayerNorm

主要参数：

- normalized_shape: 该层特征形状- eps: 分母修正项- elementwise_affine: 是否需要affine transform

nn.LayerNorm( normalized_shape, eps=1e- 05, elementwise_affine=True)

# Normalization in DL

Normalization in DL

2. Instance Normalization

起因：BN在图像生成（Image Generation）中不适用  思路：逐Instance（channel）计算均值和方差

![](images/d54308c4e56eca2a3720247ce56af7adce8042d4c9f7a08378d115f8a6db7cc9.jpg)

![](images/ea1c8204df774c413b03e9ace49d62fc7723a7719b6ac6296d23e94acd789a8c.jpg)  
Figure 1: Artistic style transfer example of Gatys et al. (2016) method.

《Instance Normalization: The Missing Ingredient for Fast Stylization》Batch Size  《Image Style Transfer Using Convolutional Neural Networks》取50篇AI必读经典前沿论文

# Normalization in DL

Normalization in DL

# nn.InstanceNorm

主要参数：

- num_features：一个样本特征数量（最重要）- eps：分母修正项- momentum：指数加权平均估计当前mean/var- affine：是否需要affine transform- track_running_stats：是训练状态，还是测试状态

nn.InstanceNorm2d( num_features, eps=1e- 05, momentum=0.1, affine=False, track_running_stats=False)

# Normalization in DL

Normalization in DL

3. Group Normalization

起因：小batch样本中，BN估计的值不准  思路：数据不够，通道来凑

注意事项：

注意事项：  1. 不再有running_mean和running_var  2. gamma和beta为逐通道（channel）的

应用场景：大模型（小batch size）任务

![](images/d1830acacb33224812c5b3f75d9cb7c8d9e230642577877e455e084344637cfb.jpg)  
Batch Size

# Normalization in DL

Normalization in DL

# nn.GroupNorm

主要参数：

num_groups: 分组数- num_channels: 通道数（特征数）- eps: 分母修正项- affine: 是否需要affine transform

nn.GroupNorm(num_groups, num_channels, eps=1e- 05, affine=True)

# Normalization in DL

Normalization in DL

小结：BN、LN、IN和GN都是为了克服Internal Covariate Shift (ICS)

![](images/90852b75e577d8e7d21b29dff0f733c054f67ed6803f48e04334f96305b8a27a.jpg)

# Normalization in DL

Normalization in DL

小结：BN、LN、IN和GN都是为了克服Internal Covariate Shift (ICS)

![](images/af015dd7a84af2d45dff5844dc297fd1eea176736b0b81e813bd0cc7f76f6d84.jpg)

# 结语

在这次课程中，学习了深度学习中的Normalization

在下次课程中，我们将会学习

模型序列化与加载

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/312cf7efd7309b45107207edff4ff45929fdb9e7495db9471e279a342286c263.jpg)  
公众号

![](images/52645d6994d2fb88da2b28858948b8015daa7b9f266125b6b282d02d7730837b.jpg)  
客服微信