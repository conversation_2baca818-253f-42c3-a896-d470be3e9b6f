# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/a23586cc8df9dd221759a51c0679366475ce25c774a069a76f86a4ac72dbb44a.jpg)  
公众号

![](images/69051d531c9714812c092770ea9619af15799027b8137e766ec763b4fd29362b.jpg)  
微信

# 图像分类一瞥

导师：余老师

# 目录

模型是如何将图像分类的？

resnet18模型inference代码

resnet18结构分析

# Image Classification

Image Classification

模型如何完成图像分类？

![](images/2b646ee51717b992fd0c31ef9a0ddebc717b61d8d6254bf2f62d1cc478be7455.jpg)

人类：RGB图像

人类：一种动物

计算机：3- d张量

计算机：字符串

# Image Classification

Image Classification

模型如何完成图像分类？

3- d张量  $\Rightarrow$  字符串

1. 类别名与标签的转换 label_name = {"ants": 0, "bees": 1}  
2. 取输出向量最大值的标号 _, predicted = torch.max(outputs.data, 1)  
3. 复杂运算 outputus = resnet18(img_tensor)

3- d张量 模型 向量

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# Image Classification

Image Classification

模型如何完成图像分类？

答：图像分类由模型与人类配合完成

模型：将数据映射到特征

人类：定义特征的物理意义，解决实际问题

3- d张量

模型 f(x)

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

强大而无脑

# Image Classification

Image Classification

图像分类的Inference(推理)步骤：1. 获取数据与标签2. 选择模型，损失函数，优化器3. 写训练代码4. 写inference代码

Inference代码基本步骤：

1. 获取数据与模型2. 数据变换，如RGB → 4D-Tensor3. 前向传播4. 输出保存预测结果

Inference阶段注意事项：

1. 确保 model处于eval状态而非training2. 设置torch.no_grad()，减少内存消耗3. 数据预处理需保持一致，RGB or BGR？

# Image Classification

Image Classification

He K, Zhang X, Ren S, et al. Deep Residual Learning for Image Recognition

![](images/1430e8257be6a8fa30352338501e63b007530cc4cea1cdc71acfb9179cab1a28.jpg)  
图像分类经典模型

![](images/462ef99dff77a135a7587f6680abdc5eb6a3b8428dbd64e3d8c9f2b27e4b40ca.jpg)  
Figure 2. Residual learning: a building block.

# Image Classification

Image Classification

图像分类经典模型 - - - Resnet  

<table><tr><td>layer name</td><td>output size</td><td>18-layer</td><td>34-layer</td><td>50-layer</td><td>101-layer</td><td>152-layer</td></tr><tr><td>conv1</td><td>112×112</td><td colspan="5">7×7, 64, stride 2</td></tr><tr><td rowspan="2">conv2_x</td><td rowspan="2">56×56</td><td colspan="5">3×3 max pool, stride 2</td></tr><tr><td>[3×3, 64
[3×3, 64]×2</td><td>[3×3, 64
[3×3, 64]×3</td><td>[1×1, 64
3×3, 64
[1×1, 256]×3</td><td>[1×1, 64
3×3, 64
[1×1, 256]×3</td><td>[1×1, 64
3×3, 64
[1×1, 256]×3</td></tr><tr><td>conv3_x</td><td>28×28</td><td>[3×3, 128
[3×3, 128]×2</td><td>[3×3, 128
[3×3, 128]×4</td><td>[1×1, 128
3×3, 128
[1×1, 512]×4</td><td>[1×1, 128
3×3, 128
[1×1, 512]×4</td><td>[1×1, 128
3×3, 128
[1×1, 512]×8</td></tr><tr><td>conv4_x</td><td>14×14</td><td>[3×3, 256
[3×3, 256]×2</td><td>[3×3, 256
[3×3, 256]×6</td><td>[1×1, 256
3×3, 256
[1×1, 1024]×6</td><td>[1×1, 256
3×3, 256
[1×1, 1024]×23</td><td>[1×1, 256
3×3, 256
[1×1, 1024]×36</td></tr><tr><td>conv5_x</td><td>7×7</td><td>[3×3, 512
[3×3, 512]×2</td><td>[3×3, 512
[3×3, 512]×3</td><td>[1×1, 512
3×3, 512
[1×1, 2048]×3</td><td>[1×1, 512
3×3, 512
[1×1, 2048]×3</td><td>[1×1, 512
3×3, 512
[1×1, 2048]×3</td></tr><tr><td></td><td>1×1</td><td colspan="5">average pool, 1000-d fc, softmax</td></tr><tr><td colspan="2">FLOPs</td><td>1.8×109</td><td>3.6×109</td><td>3.8×109</td><td>7.6×109</td><td>11.3×109</td></tr></table>

<table><tr><td>Layer (type)</td><td>Output Shape</td><td>Param #</td></tr><tr><td>Conv2d-1</td><td>[-1, 64, 112, 112]</td><td>9,408</td></tr><tr><td>BatchNorm2d-2</td><td>[-1, 64, 112, 112]</td><td>128</td></tr><tr><td>ReLU-3</td><td>[-1, 64, 112, 112]</td><td>0</td></tr><tr><td>MaxPool2d-4</td><td>[-1, 64, 56, 56]</td><td>0</td></tr><tr><td>Conv2d-5</td><td>[-1, 64, 56, 56]</td><td>36,864</td></tr><tr><td>BatchNorm2d-6</td><td>[-1, 64, 56, 56]</td><td>128</td></tr><tr><td>ReLU-7</td><td>[-1, 64, 56, 56]</td><td>0</td></tr><tr><td>Conv2d-8</td><td>[-1, 64, 56, 56]</td><td>36,864</td></tr><tr><td>BatchNorm2d-9</td><td>[-1, 64, 56, 56]</td><td>128</td></tr><tr><td>ReLU-10</td><td>[-1, 64, 56, 56]</td><td>0</td></tr><tr><td>BasicBlock-11</td><td>[-1, 64, 56, 56]</td><td>0</td></tr><tr><td>Conv2d-12</td><td>[-1, 64, 56, 56]</td><td>36,864</td></tr><tr><td>BatchNorm2d-13</td><td>[-1, 64, 56, 56]</td><td>128</td></tr><tr><td>ReLU-14</td><td>[-1, 64, 56, 56]</td><td>0</td></tr><tr><td>Conv2d-15</td><td>[-1, 64, 56, 56]</td><td>36,864</td></tr><tr><td>BatchNorm2d-16</td><td>[-1, 64, 56, 56]</td><td>128</td></tr><tr><td>ReLU-17</td><td>[-1, 64, 56, 56]</td><td>0</td></tr><tr><td>BasicBlock-18</td><td>[-1, 64, 56, 56]</td><td>0</td></tr><tr><td>Conv2d-19</td><td>[-1, 128, 28, 28]</td><td>73,728</td></tr><tr><td>BatchNorm2d-20</td><td>[-1, 128, 28, 28]</td><td>256</td></tr><tr><td>ReLU-21</td><td>[-1, 128, 28, 28]</td><td>0</td></tr><tr><td>Conv2d-22</td><td>[-1, 128, 28, 28]</td><td>147,456</td></tr><tr><td>BatchNorm2d-23</td><td>[-1, 128, 28, 28]</td><td>256</td></tr><tr><td>Conv2d-24</td><td>[-1, 128, 28, 28]</td><td>8,192</td></tr><tr><td>BatchNorm2d-25</td><td>[-1, 128, 28, 28]</td><td>256</td></tr><tr><td>ReLU-26</td><td>[-1, 128, 28, 28]</td><td>0</td></tr><tr><td>BasicBlock-27</td><td>[-1, 128, 28, 28]</td><td>0</td></tr><tr><td>Conv2d-28</td><td>[-1, 128, 28, 28]</td><td>147,456</td></tr><tr><td>BatchNorm2d-29</td><td>[-1, 128, 28, 28]</td><td>256</td></tr><tr><td>ReLU-30</td><td>[-1, 128, 28, 28]</td><td>0</td></tr><tr><td>Conv2d-31</td><td>[-1, 128, 28, 28]</td><td>147,456</td></tr><tr><td>BatchNorm2d-32</td><td>[-1, 128, 28, 28]</td><td>256</td></tr><tr><td>ReLU-33</td><td>[-1, 128, 28, 28]</td><td>0</td></tr><tr><td>BasicBlock-34</td><td>[-1, 128, 28, 28]</td><td>0</td></tr><tr><td>Conv2d-35</td><td>[-1, 256, 14, 14]</td><td>294,912</td></tr><tr><td>BatchNorm2d-36</td><td>[-1, 256, 14, 14]</td><td>512</td></tr><tr><td>ReLU-37</td><td>[-1, 256, 14, 14]</td><td>0</td></tr><tr><td>Conv2d-38</td><td>[-1, 256, 14, 14]</td><td>589,824</td></tr><tr><td>BatchNorm2d-39</td><td>[-1, 256, 14, 14]</td><td>552注公众号深度32眼tmaatedT西复论文MB:获取00篇AI必读经典前沿论文</td></tr><tr><td>Conv2d-40</td><td>[-1, 256, 14, 14]</td><td>32,768</td></tr></table>

# 结语

在这次课程中，学习了PyTorch中模型的Inference（推理）

模型进行分类的机制

在下次课程中，我们将会学习

图像分割一瞥

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/9737ae8cc2571cac4be4d186a1cf8013659166467deaefa4f991234978a142ac.jpg)  
公众号

![](images/1b7246dae4538c78967bef904d039362407596a436df73eb092accf8f5d5bdc5.jpg)  
客服微信