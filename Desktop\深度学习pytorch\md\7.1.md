# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# 图像预处理——transforms

导师：余老师

# 目录

1/transforms 运行机制2/数据标准化——transforms.normalize

# transforms

transforms

# transforms

transforms

torchvision.transforms：常用的图像预处理方法

torchvision.datasets：常用数据集的dataset实现，MNIST，CIFAR- 10，ImageNet等

torchvision.model：常用的模型预训练，AlexNet，VGG，ResNet，GoogLeNet等

torchvision：计算机视觉工具包

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# transforms

transforms

torchvision.transforms. 常用的图像预处理方法

数据中心化数据标准化缩放裁剪旋转翻转填充噪声添加灰度变换线性变换仿射变换亮度、饱和度及对比度变换

![](images/860c2e881ec5ab066108486d6f6c0342013c90ecfb0dc7a64254a8c1d3419519.jpg)

# transforms

transforms

![](images/fa3bdfd43fbbcc7d91a4f54e50c9e9eb2b2451f16a7c2e6c66fdfda3fa50be17.jpg)

# transforms

transforms

transforms.Normalize

功能：逐channel的对图像进行标准化

output = (input - mean) / std

mean: 各通道的均值

std: 各通道的标准差

inplace: 是否原地操作

transforms.Normalize(mean, std, inplace=False)

# 结语

在这次课程中，学习了数据预处理transforms的流程与机制以及数据标准化normalize

在下次课程中，我们将会学习PyTorch的

transforms的各种方法

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/72c43be1e8f8ebdfe2219dbd5f8d06cb0179e9c967d367a5348569684951b3d6.jpg)  
公众号

![](images/be526d0e2c526bfcc51a1cc1cbca9895545f8143c03af0a5b2ea5ea5683af875.jpg)  
客服微信