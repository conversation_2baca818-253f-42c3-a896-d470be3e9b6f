# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# 池化、线性、激活函数层

导师：余老师

# 目录

1 池化层——Pooling Layer2 线性层——Linear Layer3 激活函数层——Activation Layer

# 池化层

Pooling Layer

# 池化层

Pooling Layer

池化运算：对信号进行“收集”并“总结”，类似水池收集水资源，因而

得名池化层

“收集”：多变少 “总结”：最大值/平均值

![](images/1e0ae9e34da66d3954c14b6268b3bf132e451a110f9b24b443ebb1d984dd459a.jpg)

![](images/8972c07a4e521dcfff89a0ba9ed4521e1984423e395c43b74246a4f2ebd577ef.jpg)

# 池化层

Pooling Layer

nn.MaxPool2d

功能：对二维信号（图像）进行最大值池化主要参数：

- kernel_size：池化核尺寸

- stride：步长

- padding：填充个数

- dilation：池化核间隔大小

- ceil_mode：尺寸向上取整

- return_indices：记录池化像素索引

nn.MaxPool2d(kernel_size, stride=None, padding=0, dilation=1, return_indices=False, ceil_mode=False)

![](images/f3c0d5bebfc3d89b9af1be08eef0a2030ba24aca744b471c53a4a77018cf82b2.jpg)

深度之眼，获取60篇AI必读经典前沿论文

# 池化层

Pooling Layer

# nn.AvgPool2d

功能：对二维信号（图像）进行平均值池化主要参数：

- kernel_size：池化核尺寸

- stride：步长

- padding：填充个数

- ceil_mode：尺寸向上取整

- count_include_pad：填充值用于计算

nn.AvgPool2d(kernel_size, stride=None, padding=0, ceil_mode=False, count_include_pad=True, divisor override=None)

- divisor override：除法因子公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# 池化层

Pooling Layer

nn.MaxUnpool2d

功能：对二维信号（图像）进行最大值池化上采样

主要参数：

- kernel_size：池化核尺寸- stride：步长- padding：填充个数

nn.MaxUnpool2d(kernel_size, stride=None, padding=0)

forward(self, input, indices, output_size=None)

![](images/d1072cd8668a2dae2f79b479ab2177822dff253acdcf667cb4f765d02cdb68ba.jpg)

# 线性层

Linear Layer

线性层又称全连接层，其每个神经元与上一层所有神经元相连

实现对前一层的线性组合，线性变换Input = [1, 2, 3] shape = (1, 3)

[1234] W_0 = [1234] shape = (3, 4) [1234]

Hidden = Input * W_0 shape = (1, 4) = [6, 12, 18, 24]

![](images/2c914590d8400be403085a713bb5dd3b7fc479b8acbd28e54849ccaaa28595fe.jpg)

关注公众号深度之眼，后台回复论文，获取6篇AI必读经典前沿论文

# 线性层

Linear Layer

nn.Linear

nn.Linear(in_features, out_features, bias=True)

功能：对一维信号（向量）进行线性组合主要参数：

- in_features: 输入结点数- out_features: 输出结点数- bias: 是否需要偏置计算公式： $y = xW^T + bias$

![](images/4b55be419c13c7dfba5723d74e8c88f7c10653d6bcf3195a408d45400879311c.jpg)

# 激活函数层

Activation Layer

激活函数对特征进行非线性变换，赋予多层神经网络具有深度的意义

H1 = X \* W1 H2 = H1 \* W2 Output = H2 \* W3 = H1 \* W2 \* W3 = X \*(W1 \* W2 \* W3) = X \* W

![](images/a11521f10ba669ab1c6a9fea2e87d0d66374af6433cebae6ab872f72d546bd8b.jpg)

# 激活函数层

Activation Layer

# nn.Sigmoid

计算公式：y=

梯度公式：y'=y\*(1- y)

特性：

输出值在(0,1)，符合概率

导数范围是[0，0.25]，易导致梯度消失

输出为非0均值，破坏数据分布

![](images/4ff6f81ffaa5f057810eb6a9970072850e708b8e835d2cefae73175704e3391f.jpg)

# 激活函数层

Activation Layer

# nn.tanh

计算公式：y=  $\begin{array}{r}\mathbf{y} = \frac{\sin x}{\cos x} = \frac{e^x - e^{- x}}{e^{- + }e^{- x}} = \frac{2}{1 + e^{- 2x}} +1 \end{array}$  梯度公式：y'=1- y²

特性：

- 输出值在(-1,1)，数据符合0均值- 导数范围是(0,1)，易导致梯度消失

![](images/15300728a9649da1c187b2cdbc3e625f4d7da17c9fd21c70f96bdbe12ad4fa67.jpg)

# 激活函数层

Activation Layer

nn.ReLU

计算公式：y=m

梯度公式：y' =

特性：

输出值均为正数

导数是1，缓解梯

![](images/48dea8553a0a4ca74fbb6c122ff264ee38f97907f39e57f7c54ea6a808e13cd2.jpg)

炸

# 激活函数层

Activation Layer

- negative_slope: 负半轴斜率- nn.PReLU- init: 可学习斜率- nn.RReLU- lower: 均匀分布下限- upper: 均匀分布上限

![](images/394fa80ac68eafb3461b3a80fd0f1711946b0ac77ca6d119e62618360303c590.jpg)

# 结语

在这次课程中，学习了nn模块中池化层，线性层和激活函数层

在下次课程中，我们将会学习网络层权值的初始化

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/45fe37fe4564eb0d3a1580f87bec3f9fc42847dbbafea02f7749605e14ff3be1.jpg)  
公众号

![](images/8d19271a013d9f9a9ef9012ca817e6cfce6caa210a5b2b9edd177b907722b184.jpg)  
客服微信