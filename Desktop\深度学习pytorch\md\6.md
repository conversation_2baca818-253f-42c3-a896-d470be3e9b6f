# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# DataLoader与Dataset

导师：余老师

# 目录

1/人民币二分类  2/DataLoader与Dataset

# 人民币二分类

CNY Classification

# 人民币二分类

CNY Classification

![](images/222ae13dd105fae77a2a0f85b6a05467cf11c5bcf473448bbe347438ffce6f97.jpg)

# 人民币二分类

CNY Classification

![](images/a17c6a123276a33ef3ec1a2323917675e3c999c1bfcc7cfee1d60b332f71f8c6.jpg)

机器学习模型训练步骤关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# 人民币二分类

CNY Classification

![](images/ad480f32e66a43bf82abefc5d3fce59fca18608add398a7334abb29ae18d5880.jpg)

# DataLoader 与 Dataset

DataLoader and Dataset

# DataLoader

DataLoader

# DataLoaderDataLoader(dataset,

功能：构建可选代的数据装载器

- dataset: Dataset类，决定数据从哪读取及如何读取- batchsize: 批大小- num_works: 是否多进程读取数据- shuffle: 每个epoch是否乱序- drop_last: 当样本数不能被batchsize整除时，是否舍弃最后一批数据

DataLoader( dataset, batch_size=1, shuffle=False, sampler=None, batch sampler=None, num_workers=0, collate_fn=None, pin_memory=False, drop_last=False, timeout=0, worker_init_fn=None, multiprocessing_context=None)

# DataLoader

Loader

Epoch: 所有训练样本都已输入到模型中，称为一个Epoch Iteration: 一批样本输入到模型中，称之为一个Iteration Batchsize: 批大小，决定一个Epoch有多少个Iteration 样本总数：80，Batchsize：8 1 Epoch = 10 Iteration

样本总数：87，Batchsize：8

1 Epoch = 10 Iteration ? drop_last = True

1 Epoch = 11 Iteration ? drop_last = False

# Dataset

Dataset

torch.utils.data.Dataset

功能：Dataset抽象类，所有自定义的Dataset需要继承它，并且复写__getitem__()getitem：

接收一个索引，返回一个样本

class Dataset(object):

def __getitem__(self, index):    raise NotImplementedError    def __add__(self, other):        return ConcatDataset([self, other])

# DataLoader

Loader

![](images/948ab635213b95b1a3e1d8e1522773a5a445f5e843199384de3f5a10879fe4ab.jpg)

# DataLoader

DataLoader

![](images/c3e57fad89b71ccfdc56935a2187f8725725b122c266da4a0577b97a4c04e42f.jpg)

# 结语

在这次课程中，学习了纸币二分类模型训练及数据读取模块 DataLoader与DataSet的概念在下次课程中，我们将会学习PyTorch的数据预处理模块——transforms

![](images/932d4bf884f83aedd7215e8ae1378a38cad91c8065ec358b80ec2c8627ce1143.jpg)

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/312cf7efd7309b45107207edff4ff45929fdb9e7495db9471e279a342286c263.jpg)  
公众号

![](images/52645d6994d2fb88da2b28858948b8015daa7b9f266125b6b282d02d7730837b.jpg)  
客服微信