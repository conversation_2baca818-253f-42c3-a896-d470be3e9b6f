# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# 正则化之weight decay

导师：余老师

# 目录

正则化与偏差- 方差分解

pytorch中的L2正则项——weight decay

# Regularization

Regularization

# Regularization: 减小方差的策略

误差可分解为：偏差，方差与噪声之和。即误差 = 偏差 + 方差 + 噪声之和偏差度量了学习算法的期望预测与真实结果的偏离程度，即刻画了学习算法本身的拟合能力

方差度量了同样大小的训练集的变动所导致的学习性能的变化，即刻画了数据扰动所造成的影响

噪声则表达了在当前任务上任何学习算法所能达到的期望泛化误差的下界

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# Regularization

Regularization

# Regularization: 减小方差的策略

![](images/a566f0beb56efcdd8e608f7406a9fc6071a4e6f84c3f432130626529ec2198a7.jpg)

方差(Variance) 刻画数据扰动所造成的影响偏差(Bias) 刻画学习算法本身的拟合能力噪声(Noise) 当前任务任何学习算法能达到的期望泛化误差的下界

![](images/bee2dcc5d5cc9d660729a46c9ce6462e1aef68787cff4ec37564e4559655e1d8.jpg)

# Regularization

Regularization

![](images/1c4f080402de0c7415ccaba404148626d7f4addeff5ba7db8ece09c43267c054.jpg)  
Regularization: 减小方差的策略

# Regularization

Regularization

# Regularization: 减小方差的策略

损失函数：衡量模型输出与真实标签的差异

损失函数(Loss Function):

$$
Loss = f(y^{\wedge},y)
$$

代价函数(Cost Function):

$$
Cost = \frac{1}{N}\sum_{i}^{N}f(y_{i}^{\wedge},y_{i})
$$

目标函数(Objective Function):  $Obj = Cost + Regularization Term$

![](images/067322642ee4114538577a3f0457cba22e665b7e04dba3d7a0f96c91d87c147b.jpg)

# Regularization

Regularization

# Regularization: 减小方差的策略

目标函数(Objective Function):

Obj = Cost + Regularization Term

L1 Regularization Term:  $\sum_{i}^{N} |w_{i}|$   L2 Regularization Term:  $\sum_{i}^{N} w_{i}^{2}$

![](images/03464045c4f70290574774d6710288d0542cf224980dff9b79a028b6ad54fac9.jpg)

# Regularization

Regularization

# L2 Regularization = weight decay (权值衰减)

目标函数(Objective Function):

Obj = Cost + Regularization Term

$$
\begin{array}{r}O b j = L o s s + \frac{\lambda}{2} *\sum_{i}^{N}w_{i}^{2} \end{array}
$$

$$
\begin{array}{r}w_{i + 1} = w_{i} - \frac{\partial Obj}{\partial w_{i}} = w_{i} - (\frac{\partial Loss}{\partial w_{i}} +\lambda *w_{i})\\ = w_{i}(1 - \lambda) - \frac{\partial Loss}{\partial w_{i}} \end{array}
$$

![](images/fb989aec7e63bdc134211185b4a3b863212554032fb58da6068d4c8d7d7b52f2.jpg)

# 结语

在这次课程中，学习了正则化中L1和L2正则化策略

在下次课程中，我们将会学习

Dropout正则化策略

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/9d49ae0bf6cec9ceb460efc2f01286b81b6d277eb2b22f2b18ee96796c89883c.jpg)  
公众号

![](images/cca5bbe750b242ed7d0bf919f9d3b88a9fa3fa91ea5bb3aa52daab9d20dabfd5.jpg)  
客服微信