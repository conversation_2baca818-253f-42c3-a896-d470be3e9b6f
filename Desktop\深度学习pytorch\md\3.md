# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0694eb6b05212ffdb91396f3d31a6ddad22fb49b76eff0bc9b3ed9125af4767b.jpg)  
公众号

![](images/4b0da0b6d9825b23423b2b81577605bc577336413865b36f33e510de78611bde.jpg)  
微信

# 张量操作与线性回归

导师：余老师

目录

1. 张量的操作：拼接、切分、索引和变换2. 张量的数学运算3. 线性回归

# 张量的操作

Tensor Operation

# 张量操作

Tensor Operation

# 一、张量拼接与切分

1.1torch.cat()

功能：将张量按维度dim进行拼接

- tensors: 张量序列

- dim: 要拼接的维度

1.2torch.stack()

功能：在新创建的维度dim上进行拼接

- tensors: 张量序列

- dim: 要拼接的维度

torch.cat(tensors, dim=0, out=None)

torch.stack(tensors, dim=0, out=None)

# 张量操作

Tensor Operation

# 一、张量拼接与切分

1.3 torch.chunk()

功能：将张量按维度dim进行平均切分

返回值：张量列表

注意事项：若不能整除，最后一份张量小于其他张量

input: 要切分的张量 chunks: 要切分的份数 dim: 要切分的维度

torch.chunk(input, chunks, dim=0)

# 张量操作

Tensor Operation

# 一、张量拼接与切分

1.4 torch.split()

功能：将张量按维度dim进行切分

返回值：张量列表

- tensor: 要切分的张量- split_size_or_sections: 为int时，表示每一份的长度；为list时，按list元素切分- dim: 要切分的维度

torch.split(tensor, split_size_or_sections, dim=0)

# 张量操作

Tensor Operation

# 二、张量索引

2.1 torch.index_select()

功能：在维度dim上，按index索引数据

返回值：依index索引数据拼接的张量

- input: 要索引的张量- dim: 要索引的维度- index: 要索引数据的序号

torch.index_select(input, dim, index, out=None)

张量操作Tensor Operation

# 二、张量索引

2.2 torch.masked_select()

功能：按mask中的True进行索引

返回值：一维张量

input: 要索引的张量

mask: 与input同形状的布尔类型张量

torch.masked_select(input, mask, out=None)

张量操作Tensor Operation

# 三、张量变换

3.1 torch.reshape()

功能：变换张量形状

注意事项：当张量在内存中是连续时，新张量与input共享数据内存- input: 要变换的张量- shape: 新张量的形状

torch.reshape(input, shape)

张量操作Tensor Operation

# 三、张量变换

3.2 torch.transpose()

功能：交换张量的两个维度

input: 要变换的张量- dim0: 要交换的维度- dim1: 要交换的维度

3.3 torch.t()

功能：2维张量转置，对矩阵而言，等价于

torch.transpose(input, dim0, dim1)

torch.t(input)

# 张量操作

Tensor Operation

# 三、张量变换

3.4 torch.squeeze()

功能：压缩长度为1的维度（轴）

- dim: 若为None，移除所有长度为1的轴；若指定维度，当且仅当该轴长度为1时，可以被移除；

3.5 torchunsqueeze()

功能：依据dim扩展维度

- dim: 扩展的维度

torch.squeeze(input, dim=None, out=None)torch.usqueeze(input, dim, out=None)

# 张量数学运算

Tensor Math Operations

# 张量数学运算

Tensor Math Operations

一、加减乘除  二、对数，指数，幂函数

三、三角函数

torch.add()  torch.addcdiv()  torch.addcmul()  torch.sub()  torch.div()  torch.mul()

torch.log(input, out=None)  torch.log10(input, out=None)  torch.log2(input, out=None)  torch.exp(input, out=None)  torch.pow()

torch.abs(input, out=None)  torch.acos(input, out=None)  torch.cosh(input, out=None)  torch.cos(input, out=None)  torch.asin(input, out=None)  torch.atan(input, out=None)  torch.atan2(input, other, out=None)

# 张量数学运算

Tensor Math Operations

torch.add()

功能：逐元素计算 input+alpha×other

input: 第一个张量- alpha: 乘项因子- other: 第二个张量

Pythonic:

torch.addcdiv()

$$
\mathrm{out}_i = \mathrm{input}_i + \mathrm{value}\times \frac{\mathrm{tensor1}_i}{\mathrm{tensor2}_i}
$$

torch.add(input,

alpha=1,

other,

out=None)

torch.addcmul(input, value=1, tensor1, tensor2, out=None)

torch.addcmul() out_i = input_i + value × tensor1_i × tensor2_i

# 线性回归

Linear Regression

# 线性回归

Linear Regression

线性回归是分析一个变量与另外一（多）个变量之间关系的方法

因变量：y 自变量：x 关系：线性y = wx + b

分析：求解w，b

# 线性回归

Linear Regression

求解步骤：

1. 确定模型

Model: y = wx + b

2. 选择损失函数

1 m ² MSE: (y - y) m i=1

3. 求解梯度并更新w,b w = w - LR * w.grad b = b - LR * w.grad

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# 线性回归

Linear Regression

![](images/f492ca56007778ffce8187176bdde58954fdbee70c1c2fdf57f0994b9e017aae.jpg)

# 结语

在这次课程中，学习了张量的操作及线性回归实现

在下次课程中，我们将会学习PyTorch的

计算图与动态图机制

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/1f53604e97e95b36365c934b3b2603987c2aa9b965ca7c8bf4a992832ee24355.jpg)  
公众号

![](images/9ead1560ad125a03cfe3ee5f6b7821d5c7763fbee20b435a7ef599476465f789.jpg)  
客服微信