# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# PyTorch常见报错与课程总结

导师：余老师

# 目录

PyTorch常见报错

PyTorch框架训练营课程总结

# 常见报错

Common Errors

共同贡献PyTorch常见错误与坑汇总文档：https://shimo.im/docs/PvgHytYygPVGJ8Hv/ 《PyTorch常见报错/坑汇总》

微信二维码

# 常见报错

Common Errors

1.

报错：ValueError: num_samples should be a positive integer value, but got num_samples=0

可能的原因：传入的Dataset中的len(self.data_info)==0，即传入该dataloader的dataset里没有数据

解决方法：

1. 检查dataset中的路径  
2. 检查Dataset的__len__()函数为何输出为零

# 常见报错

Common Errors

2

报错：TypeError: pic should be PIL Image or ndarray. Got <class 'torch.Tensor' >

可能的原因：当前操作需要PIL Image或ndarray数据类型，但传入了Tensor

解决方法：

1. 检查transform中是否存在两次ToTensor()方法  
2. 检查transform中每一个操作的数据类型变化

# 常见报错

Common Errors

3

报错：RuntimeError: invalid argument 0: Sizes of tensors must match except in dimension 0. Got 93 and 89 in dimension 1 at /Users/<USER>/code/builder/wheel/pytorch- src/aten/src/TH/generic/THTensorMath.cpp:3616

可能的原因：dataloader的__getitem__函数中，返回的图片形状不一致，导致无法stack

解决方法：检查__getitem__函数中的操作

# 常见报错

Common Errors

4

报错：

conv: RuntimeError: Given groups=1, weight of size 6 1 5 5, expected input[16, 3, 32, 32] to have 1 channels, but got 3 channels instead

linear: RuntimeError: size mismatch, m1: [16 x 576], m2: [400 x 120] at ../aten/src/TH/generic/THTensorMath.cpp:752

可能的原因：网络层输入数据与网络的参数不匹配

解决方法：

1. 检查对应网络层前后定义是否有误  
2. 检查输入数据shape

# 常见报错

Common Errors

5

报错：AttributeError: 'DataParallel' object has no attribute 'linear'

可能的原因：并行运算时，模型被dataparallel包装，所有module都增加一个属性 module. 因此需要通过 net.module.linear调用

解决方法：

1. 网络层前加入module.

# 常见报错

Common Errors

6

报错：

RuntimeError: Attempting to deserialize object on a CUDA device but torch.cuda.is_available() is False. If you are running on a CPU- only machine, please use torch.load with map_location=torch.device('cpu') to map your storages to the CPU.

可能的原因：gpu训练的模型保存后，在无gpu设备上无法直接加载

解决方法：

1. 需要设置map_location="cpu"

# 常见报错

Common Errors

7

报错：AttributeError: Can't get attribute 'FooNet2' on <module '__main__' from '

可能的原因：保存的网络模型在当前python脚本中没有定义

解决方法：

1. 提前定义该类

# 常见报错

Common Errors

8

报错：

RuntimeError: Assertion `cur_target >= 0 && cur_target < n_classes' failed. at ../aten/src/THNN/generic/ClassNLLCriterion.c:94`

可能的原因：

1. 标签数大于等于类别数量，即不满足 `cur_target < n_classes`，通常是因为标签从1开始而不是从0开始

解决方法：

1. 修改label，从0开始

# 常见报错

Common Errors

9

报错：

RuntimeError: expected device cuda:0 and dtype Long but got device cpu and dtype Long

Expected object of backend CPU but got backend CUDA for argument #2 'weight'

可能的原因：需计算的两个数据不在同一个设备上

解决方法：采用to函数将数据迁移到同一个设备上

# 常见报错

常见报错Common Errors

10

报错：

报错：RuntimeError: DataLoader worker (pid 27) is killed by signal: Killed. Details are lost due to multiprocessing. Rerunning with num_workers=0 may give better error trace.

可能原因：内存不够（不是gpu显存，是内存）

解决方法：申请更大内存

# 课程总结 Course Summary

![](images/742b4d4aaf5443a06bfa971ed0522a0703a0ab02a18fa6bc001e27fea5daf6b9.jpg)

# 机器学习模型训练步骤

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# 课程总结

Course Summary

![](images/24cc9807d7f429d2de1141fcf63bd28cd5688d5e963f512b9b0630dc276ec941.jpg)

# 结语

在这次课程中，学习了PyTorch中常见的报错

在下次课程中，我们将会学习

图像分类任务

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/1325f4fac2a7cc08fead6e762c0bd4501535d5bd3d7a13032631423ec28a2aa9.jpg)  
公众号

![](images/246966bb09491aade2f93edc91a08e23e9a3a79ce85900a1af04dfc8ff9c6805.jpg)  
客服微信