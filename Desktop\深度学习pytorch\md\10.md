# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# 卷积层

导师：余老师

# 目录

1d/2d/3d卷积  2卷积- nn.Conv2d()  3转置卷积- nn.ConvTranspose

# 1d/2d/3d Convolution

Dimension of Convolution

# 1d/2d/3d Convolution

Dimension of Convolution

卷积运算：卷积核在输入信号（图像）上滑动，相应位置上进行乘加卷积核：又称为滤波器，过滤器，可认为是某种模式，某种特征。

卷积过程类似于用一个模版去图像上寻找与它相似的区域，与卷积核模式越相似，激活值越高，从而实现特征提取

![](images/e804a2060b47196d11fb3fd395e88149ac4b82fd66117138e2fcfb24e1efa45e.jpg)

# 1d/2d/3d Convolution

Dimension of Convolution

AlexNet卷积核可视化，发现卷积核学习到的是边缘，条纹，色彩这一些细节模式

![](images/28664bcda2de4c29d6d396bd27c5344cc726ae866ca96ac420c9d1d6d1159132.jpg)

# 1d/2d/3d Convolution

Dimension of Convolution

卷积维度：一般情况下，卷积核在几个维度上滑动，就是几维卷积

![](images/18abe4eec97c3bce44b3bc4b715ee494884569a05f0f6b59d66526329adbf59c.jpg)

![](images/76d5be62d4b00523f3b03c932424779c6caffc52959c14adcc4af1e563e0fc6c.jpg)

![](images/1edeed16be57ccfb4c71f856e04847ae60d1c454ff5edfd6b29a069628fe6eea.jpg)

# nn.Conv2d

nn.Conv2d

# nn.Conv2d

nn.Conv2d

# nn.Conv2d

功能：对多个二维信号进行二维卷积

主要参数：

in_channels：输入通道数out_channels：输出通道数，等价于卷积核个数

kernel_size：卷积核尺寸stride：步长padding：填充个数

nn.Conv2d(in_channels, out_channels, kernel_size, stride=1, padding=0, dilation=1, groups=1, bias=True, padding_mode='zeros')

dilation：空洞卷积大小 groups：分组卷积设置 bias：偏置

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# nn.Conv2d

nn.Conv2d

nn.Conv2d

功能：对多个二维信号进行二维卷积

主要参数：

in_channels：输入通道数out_channels：输出通道数，等价于卷积核个数

![](images/4f9fe34f06c2f255287bcb5d92508cd8c363855c52aef82476d5e2b8203059e1.jpg)

kernel_size：卷积核尺寸 stride：步长 padding：填充个数

dilation：空洞卷积大小 groups：分组卷积设置

bias：偏置

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# nn.Conv2d

nn.Conv2d

nn.Conv2d

功能：对多个二维信号进行二维卷积

主要参数：

in_channels：输入通道数out_channels：输出通道数，等价于卷积核个数

![](images/daa5a7c0050d3d40343c9c144d6ee5f8727d65961c4512aad18aafd1aa815d3c.jpg)

kernel_size：卷积核尺寸 stride：步长 padding：填充个数

dilation：空洞卷积大小 groups：分组卷积设置

bias：偏置

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# nn.Conv2d

nn.Conv2d

nn.Conv2d

功能：对多个二维信号进行二维卷积

主要参数：

in_channels：输入通道数out_channels：输出通道数，等价于卷积核个数

kernel_size：卷积核尺寸

stride：步长

padding：填充个数

![](images/9b08bb614375efed1f8f7193d5ecdb403da7510681f7d8bdb684e3b69ebbaf75.jpg)

dilation：空洞卷积大小

groups：分组卷积设置

bias：偏置

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# nn.Conv2d

nn.Conv2d

# nn.Conv2d

功能：对多个二维信号进行二维卷积

![](images/bd1b8a6fda947da82072521089ca46fb2954c1f9b34bda5c4d2bf0e6e15e5c50.jpg)

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# nn.Conv2d

nn.Conv2d

nn.Conv2d

功能：对多个二维平面信号进行二维卷积

尺寸计算：

简化版：outsize= Insize- kernelsize +1 stride

完整版：

![](images/ddc439dd6008d78f16b3526030164d3fa062719a56b304f89f3a4357efa7dbf2.jpg)

![](images/7e886c0a3af3df6884010dff2baf07b380fc7524e9bf27e722a76444c27cff71.jpg)

# 1d/2d/3d Convolution

Dimension of Convolution

卷积维度：一般情况下，卷积核在几个维度上滑动，就是几维卷积

![](images/ace63fccc48b82c5de87b12aec3d2b4e53d8fd5955067c646a1ebda643c39325.jpg)

![](images/7412b3b9f8a0e356c7966340c6cd616e9aa1567bc272d48570ee404ec5fef0d9.jpg)

回复论文，获取60篇AI必读经典前沿论文

# 转置卷积

Transpose Convolution

# 转置卷积

Transpose Convolution

转置卷积又称为反卷积(Deconvolution)和部分跨越卷积(Fractionally- strided Convolution)，用于对图像进行上采样(UpSample)

为什么称为转置卷积？

假设图像尺寸为4*4，卷积核为3*3，padding=0，stride=1正常卷积：

图像：I16*1 卷积核：K4*16 输出：O4*1 = K4*16 * I16*1

![](images/2607aea12d12fb1ddeb161c7a526834fcef9cc7eac60cfd6880505ae5b2b1434.jpg)

# 转置卷积

Transpose Convolution

为什么称为转置卷积？

正常卷积：

假设图像尺寸为4*4，卷积核为3*3，padding=0，stride=1  图像：I_{16*1} 卷积核：K_{4*16} 输出：O_{4*1} = K_{4*16} * I_{16*1}

转置卷积：

假设图像尺寸为2*2，卷积核为3*3，padding=0，stride=1  图像：I_{4*1} 卷积核：K_{16*4} 输出：O_{16*1} = K_{16*4} * I_{4*1}

![](images/e0354b3df09dc57702eed013474ab1f48e78785880ed32308ee6060126f80049.jpg)

# 转置卷积

Transpose Convolution

nn.ConvTranspose2d

功能：转置卷积实现上采样

主要参数：

in_channels: 输入通道数

out_channels: 输出通道数

kernel_size: 卷积核尺寸

stride: 步长

padding: 填充个数

nn.ConvTranspose2d(in_channels, out_channels, kernel_size, stride=1, padding=0, output_padding=0, groups=1, bias=True, dilation=1, padding_mode='zeros')

dilation: 空洞卷积大小- groups: 分组卷积设置- bias: 偏置

# 转置卷积

Transpose Convolution

nn.ConvTranspose2d

功能：转置卷积实现上采样

尺寸计算：

简化版：outsize = (insize - 1) * stride + kernel_size outsize =  $\frac{In_{size} - kernel_{size}}{stride} + 1$

完整版：

$H_{out} = (H_{in} - 1)\times \mathrm{stride}[0] - 2\times \mathrm{padding}[0] + \mathrm{dilaton}[0]\times (\mathrm{kernel\_size}[0] - 1) + \mathrm{output\_padd}[0] + 1$

# 结语

在这次课程中，学习了nn模块中卷积层

在下次课程中，我们将会学习nn中其他常用网络层

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/683af8f34f98a713762af4a7ecccbe1cd9d3766e89f03069ab958dc51aa9dff7.jpg)  
公众号

![](images/5cc95f1a043eb1f7dbe65b3209ecceb831b328029335ca628d9f2bea63ed83a0.jpg)  
客服微信