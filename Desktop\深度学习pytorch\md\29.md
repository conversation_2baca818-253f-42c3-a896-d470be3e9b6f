# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/a23586cc8df9dd221759a51c0679366475ce25c774a069a76f86a4ac72dbb44a.jpg)  
公众号

![](images/69051d531c9714812c092770ea9619af15799027b8137e766ec763b4fd29362b.jpg)  
微信

# 图像分割一瞥

导师：余老师

目录

图像分割是什么？

模型是如何将图像分割的？

深度学习图像分割模型简介

训练Unet完成人像抠图

# Image Segmentation

Image Segmentation

# 图像分割：将图像每一个像素分类

![](images/494585765ee7cbfe96dfb69a0519b09a819f3f32e86ea6b4441fbff4cca5e6fc.jpg)

# Image Segmentation

Image Segmentation

图像分割分类：

1. 超像素分割：少量超像素代替大量像素，常用于图像预处理2. 语义分割：逐像素分类，无法区分个体3. 实例分割：对个体目标进行分割，像素级目标检测4. 全景分割：语义分割结合实例分割

![](images/c18eed8d4ca13e0f96ac9aaa06f091bc6adf71f8e8cfefc7b5fd784ba407713c.jpg)  
Superpixels（超像素分割）

![](images/4c3b9f925159df6b9bab72022c05d1731c3b11871ed2e0aad76e9f3244b0c233.jpg)

![](images/0f6e92a79b1c4db0d2a1d36168519ee6d92b6edee262bb06a071f3e5ae3e1efe.jpg)  
Semantic Segmentation（语义分割）

![](images/a513a4e4347bde00a9712d071302d90e13650a82241f10282a8360503e38a2e9.jpg)  
Panoptic Segmentation（全景分割）

# Image Segmentation

Image Segmentation

模型如何完成图像分割？

![](images/17d3d0151c781325e6d7db5d3c9c3cbaaa1417061101a964a719266d15707bc7.jpg)

![](images/8536706ff92096d42567896e5218fc6c2cee1b7f6674553f9f0b4cf200a38733.jpg)

![](images/7695b1981280e410e984db9cbf236fc97fe4fb71fb6c16475282d3734b96cf30.jpg)

# Image Segmentation

Image Segmentation

模型如何完成图像分割？

答：图像分割由模型与人类配合完成

模型：将数据映射到特征

人类：定义特征的物理意义，解决实际问题

![](images/c77b92684d7beb9f17ba79bc2813ed2ffbc9f624d375a520d5de878a150cc749.jpg)

图像分类输出向量（特征图）

# Image Segmentation

Image Segmentation

PyTorch- Hub——PyTorch模型库，有大量模型供开发者调用

1. torch.hub.load('pytorch/vision', 'deeplabv3_resnet101', pretrained=True) model = torch.hub.load(github, model, *args, **kwargs)

功能：加载模型

主要参数：

- github: str, 项目名, eg: pytorch/vision, <repo_owner/repo_name[:tag_name]>- model: str, 模型名

2. torch.hub.list(github, force_reload=False)

3. torch.hub.help(github, model, force_reload=False)

# Image Segmentation

Image Segmentation

图像分割的思考

![](images/684f99f14c101f49c2d71f07dd06ddfc11fd2bc32d991a393d1a682b29e08113.jpg)

![](images/f89b3785d7b96e441657641a9e090ff44c035556e7cfb9b844670bf7bfb8d6e5.jpg)

# Image Segmentation

Image Segmentation

深度学习中的图像分割模型

最主要贡献：利用全卷积完成pixelwise prediction

![](images/055df6615a2424755668b03bc4c1b5377ca02f517c60b9c40217e71e655eac1e.jpg)  
Figure 1. Fully convolutional networks can efficiently learn to make dense predictions for per-pixel tasks like semantic segmentation.

# Image Segmentation

Image Segmentation深度学习中的图像分割模型

深度学习中的图像分割模型

![](images/6e67e665d188244b4d4caaa54c8238dd601a6bfebe4e54362df611b43c5b4192.jpg)

《U- Net: Convolutional Networks for Biomedical Image Segmentation》

![](images/0c4c46f1cb2d3b332cf004b902f0b39e3272919ca5269b991cab2d6d1cdc4ba8.jpg)

Fig. 1. U- net architecture (example for 32x32 pixels in the lowest resolution). Each blue box corresponds to a multi- channel feature map. The number of channels is denoted on top of the box. The x- y- size is provided at the lower left edge of the box. White boxes represent copied feature maps. The arrows denote the different operations.

# Image Segmentation

Image Segmentation

深度学习中的图像分割模型

DeepLab系列——V1

主要特点：1. 孔洞卷积：借助孔洞卷积，增大感受野2. CRF：采用CRF进行mask后处理

![](images/1c3abd1fd29c687f9dab96d7c37755f3cbd9d1044ecfc9a32c89cc95789f286a.jpg)  
Figure 1: Illustration of the hole algorithm in 1-D, when kernel_size = 3, input stride = 2, and output stride = 1.

# Image Segmentation

Image Segmentation

# 深度学习中的图像分割模型

DeepLab系列——V2

# 主要特点：

1. ASPP (Atrous spatial pyramid pooling)：解决多尺度问题

《DeepLab- Semantic Image Segmentation with Deep Convolutional Nets, Atrous Convolution, and Fully Connected CRFs》

# Image Segmentation

Image Segmentation

# 深度学习中的图像分割模型

DeepLab系列——V3

![](images/9b741adc62b6a0b5827cf9e48f75bb21bf608a1b0e43fdd480bf55e3a34759d0.jpg)

# 主要特点：1.孔洞卷积的串行2.ASPP的并行

《DeepLabv3- Rethinking Atrous Convolution for Semantic Image Segmentation》50篇 AI 必读经典前沿论文

# Image Segmentation

Image Segmentation

# 深度学习中的图像分割模型

DeepLab系列——V3

![](images/0d46850d1e554d437be692abafdc900dc17e13bda5102e516bddf198dae9fedc.jpg)

# 主要特点：1.孔洞卷积的串行2.ASPP的并行

《DeepLabv3- Rethinking Atrous Convolution for Semantic Image Segmentation》50篇 AI必读经典前沿论文

# Image Segmentation

Image Segmentation

# 深度学习中的图像分割模型

DeepLab系列——V3

![](images/cbdfdb0389d190e31332d5a998de63a80c7b55459102ae36c8e4c641fa1553f4.jpg)

# 主要特点：1.孔洞卷积的串行2.ASPP的并行

《DeepLabv3- Rethinking Atrous Convolution for Semantic Image Segmentation》50篇AI必读经典前沿论文

# Image Segmentation

Image Segmentation

# 深度学习中的图像分割模型

DeepLab系列——V3+

![](images/259e4c538f9a0551367800f97804fbc48f616407ea2a52561aa9cd15e1c7baec.jpg)

主要特点：deeplabv3基础上加上Encoder- Decoder思想

《DeepLabv3- Rethinking Atrous Convolution for Semantic Image Segmentation》50篇AI必读经典前沿论文

# Image Segmentation

Image Segmentation

# 深度学习中的图像分割模型

《Deep Semantic Segmentation of Natural and Medical Images: A Review》2019

# 图像分割资源：

https://github.com/shawnbit/unet- familyhttps://github.com/yassouali/pytorch_segmentation

Table 1: A summary of papers for semantic segmentation of natural images applied to PASCAL VOC 2012 dataset.  

<table><tr><td>Paper</td><td>Type of Improvement</td><td>Dataset(s) evaluated on</td><td>PASCAL VOC 2012 mean IoU</td></tr><tr><td>SegNet (2015) [103]</td><td>Architecture</td><td>PASCAL VOC, CamVid, SUN RGB-D</td><td>59.1%</td></tr><tr><td>FCN (2014) [86]</td><td>Architecture</td><td>PASCAL VOC, NYUDv2, SIFT Flow</td><td>62.2%</td></tr><tr><td>Luc at al. (2016) [87]</td><td>Adversarial Segmentation</td><td>PASCAL VOC, Stanford Background</td><td>73.3%</td></tr><tr><td>Lovász-Softmax Loss (2017) [11]</td><td>Loss</td><td>PASCAL VOC, Cityscapes</td><td>76.44%</td></tr><tr><td>Large Kernel Matters (2017) [107]</td><td>Architecture</td><td>PASCAL VOC, Cityscapes</td><td>82.2%</td></tr><tr><td>Deep Layer Cascade (2017) [78]</td><td>Architecture</td><td>PASCAL VOC, Cityscapes</td><td>82.7%</td></tr><tr><td>TuSimple (2017) [147]</td><td>Architecture</td><td>PASCAL VOC, KITTI Road Estimation</td><td>83.1%</td></tr><tr><td>RefineNet (2016) [82]</td><td>Architecture</td><td>PASCAL VOC, PASCAL Context, Person-Part, NYUDv2, SUN RGB-D, Cityscapes, ADE20K</td><td>84.2%</td></tr><tr><td>ResNet-38 (2016) [157]</td><td>Architecture</td><td>PASCAL VOC, PASCAL Context, Cityscapes</td><td>84.9%</td></tr><tr><td>PSPNet (2016) [71]</td><td>Architecture</td><td>PASCAL VOC, Cityscapes</td><td>85.4%</td></tr><tr><td>Auto-DeepLab (2019) [85]</td><td>Architecture Search</td><td>PASCAL VOC, ADE20K, Cityscapes</td><td>85.6%</td></tr><tr><td>IDW-CNN (2017) [146]</td><td>Architecture</td><td>PASCAL VOC</td><td>86.3%</td></tr><tr><td>SDN+ (2019) [32]</td><td>Architecture</td><td>PASCAL VOC, CamVid, Gatech</td><td>86.6%</td></tr><tr><td>DIS (2017) [88]</td><td>Architecture</td><td>PASCAL VOC</td><td>86.8%</td></tr><tr><td>DeepLabV3 (2017) [21]</td><td>Architecture</td><td>PASCAL VOC</td><td>86.9%</td></tr><tr><td>MSCI (2018) [81]</td><td>Architecture</td><td>PASCAL VOC, PASCAL Context, NYUDv2, SUN RGB-D</td><td>88.0%</td></tr><tr><td>DeepLabV3+ (2018) [3]</td><td>Architecture</td><td>PASCAL VOC, Cityscapes</td><td>89.0%</td></tr></table>

关注公众号深度之眼，后台回复论文，获取50篇AI必读经典前沿论文

# Image Segmentation

Image Segmentation

Unet实现人像抠图(Portrait Matting)

![](images/a44ca1dea3ae0fb2c6c9f2b7933d731c432cb96a4cbc31ea2a56b5cff82ca45c.jpg)

![](images/c5fc35fc048440581e63de3744cd07801ab8bf7e37cc1f146e01c0b656865566.jpg)

![](images/6626b0e3ee3e916164d3d681fe0c82d76e8dc6535b4ae67ccffdf6348379f61f.jpg)

![](images/e55c335bda0beb974f30d4936949e71014aae5226865fbfe540a597172a6bff7.jpg)

# 结语

在这次课程中，学习了PyTorch中图像分割模型的使用

在下次课程中，我们将会学习

图像目标检测一瞥

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/1c37d92dc3b6eff2dcf6f259e03aa8aae3e2b66a288f21e6b75579a5165500f1.jpg)  
公众号

![](images/f0d4381f2b827caeafd226875789bcdbdd1410b04979e9b13fc4c61ed599bbdd.jpg)  
客服微信