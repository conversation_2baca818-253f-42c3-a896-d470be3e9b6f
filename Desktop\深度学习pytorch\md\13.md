# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# 损失函数

导师：余老师

# 目录

1 损失函数概念  2 交叉熵损失函数  3 NLL/BCE/BCEWithLogits Loss

# 损失函数是什么

What is Loss Function?

# 损失函数是什么

What is Loss Function

损失函数：衡量模型输出与真实标签的差异

损失函数(Loss Function):

$$
Loss = f(y^{\wedge},y)
$$

代价函数(Cost Function):

$$
Cost = \frac{1}{N}\sum_{i}^{N}f(y_{i}^{\wedge},y_{i})
$$

目标函数(Objective Function):  $Obj = Cost + \text{Regularization}$

![](images/ed4f9465b269e9c5ef06ee00e978ed68552eb0b708b7fc9a574ac1df03fff204.jpg)

# 损失函数是什么

What is Loss Function

class _Loss(Module):

def _init__(self, size_average=None, reduce=None, reduction='mean'):    super(_Loss, self).__init__()    if size_average is not None or reduce is not None:        self.reduction = _ReductionLegacy_get_string(size_average, reduce)    else:        self.reduction = reduction

# 损失函数

Loss Function

1、nn.CrossEntropyLoss

功能：nn.LogSoftmax()与nn.NLLLoss()结合，进行交叉熵计算

主要参数：

- weight: 各类别的loss设置权值- ignore_index: 忽略某个类别- reduction: 计算模式，可为none/sum/mean- none- 逐个元素计算sum- 所有元素求和，返回标量mean- 加权平均，返回标量

nn.CrossEntropyLoss(weight=None, size_average=None, ignore_index=- 100, reduce=None, reduction='mean')

$$
\begin{array}{r}\mathrm{H}(P,Q) = -\sum_{i = 1}^{N}P(x_i)logQ(x_i) \end{array}
$$

$$
\mathrm{loss}(x,class) = -\log \left(\frac{\exp(x[class])}{\sum_j\exp(x[j])}\right) = -x[class] + \log \left(\sum_j\exp (x[j])\right)
$$

$$
\mathrm{loss}(x,class) = \mathrm{weight}[\mathrm{class}]\left(-x[\mathrm{class}] + \mathrm{log}\left(\sum_j\mathrm{exp}(x[j])\right)\right)
$$

# 损失函数

Loss Function

交叉  $\mathbf{\Psi} = \mathbf{\Psi}$  信息  $^+$  相对

交叉  $\begin{array}{r}\mathrm{H}(P,Q) = - \sum_{i = 1}^{N}P(x_i)\log Q(x_i) \end{array}$

自信息：  $\operatorname {I}(x) = - \log [P(x)]$

$\begin{array}{r}\mathrm{H}(\mathrm{P}) = E_{x\sim p}[I(x)] = - \sum_{i}^{N}P(x_{i})\log P(x_{i}) \end{array}$

相对：  $\begin{array}{r}D_{KL}(P,Q) = E_{x\sim p}\left[\log \frac{P(x)}{Q(x)}\right] \end{array}$

![](images/8f0de8a8f3a0b91bcee2ca869a7c3996ac87abd426294369fd063b6efca9e810.jpg)

N = P(x)[logP(x)- logQ(x)] =1 =∑P(x)logP(x)- ∑P(x)logQ(x) 交叉  $\begin{array}{r}\mathrm{H}(P,Q) = D_{KL}(P,Q) + \mathrm{H}(P) \end{array}$  =H(P,Q)- H(P) 关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# 损失函数

Loss Function

2、nn.NLLLoss

功能：实现负对数似然函数中的负号功能

主要参数：

- weight: 各类别的loss设置权值- ignore_index: 忽略某个类别- reduction: 计算模式，可为none/sum/mean- none- 逐个元素计算sum- 所有元素求和，返回标量mean- 加权平均，返回标量

nn.NLLLoss(weight=None, size_average=None, ignore_index=- 100, reduce=None, reduction='mean')

$$
\ell (x,y) = L = \{l_1,\ldots ,l_N\}^\top ,\quad l_n = -w_{yn}x_{n,y_n}
$$

# 损失函数

Loss Function

3、nn.BCELoss

功能：二分类交叉

注意事项：输入值取值在[0,1]

主要参数：

weight：各类别的loss设置权值

ignore_index：忽略某个类别

reduction：计算模式，可为none/sum/mean

none- 逐个元素计算

sum- 所有元素求和，返回标量  mean- 加权平均，返回标量

nn.BCELoss(weight=None, size_average=None, reduce=None, reduction='mean')

$$
l_{n} = -w_{n}\left[y_{n}\cdot \log x_{n} + (1 - y_{n})\cdot \log (1 - x_{n})\right]
$$

# 损失函数

Loss Function

4、nn.BCEWithLogitsLoss

功能：结合Sigmoid与二分类交叉熵

注意事项：网络最后不加sigmoid函数

主要参数：

- pos_weight：正样本的权值- weight：各类别的loss设置权值- ignore_index：忽略某个类别- reduction：计算模式，可为none/sum/meannone-逐个元素计算sum-所有元素求和，返回标量

nn.BCEWithLogitsLoss(weight=None, size_average=None, reduce=None, reduction='mean', pos_weight=None)

$$
l_{n} = -w_{n}[y_{n}\cdot \log \sigma (x_{n}) + (1 - y_{n})\cdot \log (1 - \sigma (x_{n}))]
$$

# 结语

在这次课程中，学习了损失函数的概念，以及4种损失函数

在下次课程中，我们将会学习pytorch中其余14种损失函数

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/4a7687106c2c2191364aaced4c8cb020fbeab35ba9b828380d6863554de641de.jpg)  
公众号

![](images/00d053dabbf3db0bc4aebab266b71d98b95c390888134b6ff16e8bc84153dd9b.jpg)  
客服微信