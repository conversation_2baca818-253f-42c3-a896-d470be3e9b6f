# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# autograd与逻辑回归

导师：余老师

# 目录

1/ torch.autograd  2/逻辑回归

# autograd—自动求导系统

torch.autograd

# autograd

autograd

# torch.autograd.backward

功能：自动求取梯度

- tensors: 用于求导的张量，如 loss- retain_graph : 保存计算图- create_graph : 创建导数计算图，用于高阶求导- grad_tensors: 多梯度权重

torch.autograd.backward(tensors, grad_tensors=None, retain_graph=None, create_graph=False)

# autograd

autograd

计算图与梯度求导 $y = (x + w)\times (w + 1)$ a=x+w b=w+1y=a\*b $\frac{\partial y}{\partial w} = \frac{\partial y}{\partial a}\frac{\partial a}{\partial w} +\frac{\partial y}{\partial b}\frac{\partial b}{\partial w}$ $= b*1 + a*1$ $= b + a$ $= (w + 1) + (x + w)$ $= 2*w + x + 1$ $= 2*1 + 2 + 1 = 5$

![](images/3feec7bdcb192ffb7e111321d1ab5d20cbed41bc4a3f1d7e5c87e9e16568f77b.jpg)

# autograd

autograd

# torch.autograd.grad

功能：求取梯度

- outputs: 用于求导的张量，如 loss- inputs: 需要梯度的张量- create_graph: 创建导数计算图，用于高阶求导- retain_graph: 保存计算图- grad_outputs: 多梯度权重

torch.autograd.grad(outputs, inputs, grad_outputs=None, retain_graph=None, create_graph=False)

# autograd

autograd

autograd小贴士：

1. 梯度不自动清零

2. 依赖于叶子结点的结点，requires_grad默认为True

3. 叶子结点不可执行in-place

# 逻辑回归

Logistic Regression

# 逻辑回归Logistic Regression

逻辑回归是线性的二分类模型模型表达式：

y=f（WX+b）1f(x)=1+e- x

f(x)称为Sigmoid函数，也称为Logistic函数

$$
class = \left\{ \begin{array}{ll}0, & 0.5 > y\\ 1, & 0.5\leq y \end{array} \right.
$$

![](images/e0509aef13930cb768b5697856922744539f13b292f59dd88376f8db0816746c.jpg)

# 逻辑回归Logistic Regression

# 逻辑回归

$$
\begin{array}{l}y = f(WX + b)\\ = \frac{1}{1 + e^{-(WX + b)}}\\ f(x) = \frac{1}{1 + e^{-x}} \end{array}
$$

![](images/25489b41633ada91d526e794cfa2929635ae238f7f6883980526f6fd4126acfd.jpg)

线性回归是分析自变量x与因变量y(标量)之间关系的方法逻辑回归是分析自变量x与因变量y(概率)之间关系的方法

# 逻辑回归Logistic Regression

# 对数几率回归

# 线性回归

![](images/4d572988309c054727c39fbbb813bc9e2655f4436a5cdcb8b276852414af727a.jpg)

自变量：X因变量：y关系：y = WX + b

# 对数回归

In y = WX + b

# 逻辑回归Logistic Regression

![](images/0e8342304315f9f76ccc45cd2716f4e3a2884d32b16beb9c95d8a5a0c37df89e.jpg)

# 逻辑回归Logistic Regression

![](images/1a91f82999849e14bdd2c0ba58a60cbc04b8b36481b77d2b2c4839c534c47f1e.jpg)

机器学习模型训练步骤关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# 结语

在这次课程中，学习了autogard与逻辑回归以及模型训练5个步骤

在下次课程中，我们将会学习PyTorch的

数据读取模块DataSet与DataLoader

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/f0acdc96b0b58565af0dac192aed66e467661fff319fbe440d6fbb9384481b31.jpg)  
公众号

![](images/e0e6083815747286b3d72b75c2c32f1f326d8a817a2c04c2b9f4a7dcbefcee88.jpg)  
客服微信