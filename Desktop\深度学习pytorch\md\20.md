# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# TensorBoard使用（二）

导师：余老师

0_Convlayer_split_in_channel runs/Oct25_22- 46- 03_tingsongdeMacBookPro.localtest_your_comment

step 32

Fri Oct 25 2019 22:46:06 GMT+0800 (CST)

![](images/8cff32f7b7cbe630a581c79df75aa17e504effcf685b9f3a9e606b7195601ed7.jpg)

0_all

0_all

runs/Oct25_22- 46- 03_tingsongdeMacBookPro.localtest_your_comment

step 322 Fri Oct 25 2019 22:46:06 GMT+0800 (CST)

![](images/0570e224357159a4168e41141a7851a9f81a5bf674d7f0f033dfd287c23bcc56.jpg)

feature_map_in_conv1

feature_map_in_conv1 runs/Oct25_22- 46- 10_tingsongdeMacBook- Pro.localtest_your_comment

step 322 Fri Oct 25 2019 22:46:12 GMT+0800 (CST)

![](images/2cc3ff37a011a74ea8c44fe6e2b63d160f0735f2c5c951b410b91d58da61395a.jpg)

# TensorBoard

TensorBoard

SummaryWriter

4. add_image()

功能：记录图像

- tag: 图像的标签名，图的唯一标识- img_tensor: 图像数据，注意尺度- global_step: x轴- dataformats: 数据形式，CHW，HWC，HW

add_image(tag, img_tensor, global_step=None, walltime=None, dataformats='CHW')

# TensorBoard

TensorBoard

torchvision.utils.make_grid

功能：制作网格图像

- tensor: 图像数据，B*C*H*W形式- nrow: 行数（列数自动计算）- padding: 图像间距（像素单位）- normalize: 是否将像素值标准化- range: 标准化范围- scale_each: 是否单张图维度标准化- pad_value: padding的像素值

make_grid(tensor, nrow=8, padding=2, normalize=False, range=None, scale_each=False, pad_value=0)

# TensorBoard

TensorBoard

SummaryWriter

5. add_graph()

功能：可视化模型计算图

- model: 模型，必须是 nn.Module- input_to_model: 输出给模型的数据- verbose: 是否打印计算图结构信息

add_graph(model, input_to_model=None, verbose=False)

# TensorBoard

TensorBoard

torcshummary

功能：查看模型信息，便于调试

model: pytorch模型- input_size: 模型输入- size- batch_size: batch size- device: "cuda" or "cpu"

summary(model, input_size, batch_size=- 1, device="cuda")

github: https://github.com/sksq96/pytorch- summary

# 结语

在这次课程中，学习了卷积核与特征图可视化

网络模型可视化

在下次课程中，我们将会学习

hook函数机制与CAM可视化算法

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/b86f815a44f04804c925a082570133df08eefd3a5ef1031b5ed22cb58a8b6894.jpg)  
公众号

![](images/5ecbeadb9b968bef233ea3b652fc067d9456b29da8f167b01276c16e1a5ff64d.jpg)  
客服微信