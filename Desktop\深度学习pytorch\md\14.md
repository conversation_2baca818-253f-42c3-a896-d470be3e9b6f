# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# 损失函数

导师：余老师

# 目录

5. nn.L1Loss  
6. nn.MSELoss  
7. nn.SmoothL1Loss  
8. nn.PoissonNLLLoss  
9. nn.KLDivLoss  
10. nn.MarginRankingLoss  
11. nn.MultiLabelMarginLoss  
12. nn.SoftMarginLoss  
13. nn.MultiLabelSoftMarginLoss  
14. nn.MultiMarginLoss  
15. nn.TripletMarginLoss  
16. nn.HingeEmbeddingLoss  
17. nn.CosineEmbeddingLoss  
18. nn.CTCLoss

# 损失函数

Loss Function

5、nn.L1Loss

功能：计算inputs与target之差的绝对值

6、nn.MSELoss

功能：计算inputs与target之差的平方

主要参数：

- reduction：计算模式，可为none/sum/mean  none- 逐个元素计算  sum- 所有元素求和，返回标量  mean- 加权平均，返回标量

nn.L1Loss(size_average=None, reduce=None, reduction='mean')

$$
l_{n} = |x_{n} - y_{n}|
$$

nn.MSELoss(size_average=None, reduce=None, reduction='mean')

$$
l_{n} = (x_{n} - y_{n})^{2}
$$

# 损失函数

Loss Function

# 7、SmoothL1Loss

![](images/1a36a0d68de35390f859a2668eb769d83e4b86e87e6ce370e257bdaa777a1802.jpg)

nn.SmoothL1Loss(size_average=None, reduce=None, reduction='mean')

$$
\mathrm{loss}(x,y) = \frac{1}{n}\sum_{i}z_{i}
$$

$$
z_{i} = \left\{ \begin{array}{ll}0.5(x_{i} - y_{i})^{2}, & \mathrm{if} |x_{i} - y_{i}|< 1\\ |x_{i} - y_{i}| - 0.5, & \mathrm{otherwise} \end{array} \right.
$$

# 损失函数

Loss Function

# 8、PoissonNLLLoss

功能：泊松分布的负对数似然损失函数

# 主要参数：

- log_input：输入是否为对数形式，决定计算公式- full：计算所有loss，默认为False- eps：修正项，避免log（input）为nan

nn.PoissonNLLLoss(log_input=True, full=False, size_average=None, eps=1e- 08, reduce=None, reduction='mean')

log_input = True  loss(input, target) = exp(input) - target * input  log_input = False  loss(input, target) = input - target * log(input + eps)

# 损失函数

Loss Function

9、nn.KLDivLoss

功能：计算KLD（divergence），KL散度，相对熵

注意事项：需提前将输入计算log- probabilities，如通过nn.logsoftmax()

主要参数：

注意事项：需提前将输入计算log- probabilities，如通过nn.logsoftmax()主要参数：- reduction : none/sum/mean/batchmean- batchmean- batchsize维度求平均值- none- 逐个元素计算- sum- 所有元素求和，返回标量- mean- 加权平均，返回标量

nn.KLDivLoss(size_average=None, reduce=None, reduction='mean')

$$
\begin{array}{rl} & D_{KL}(P\| Q) = E_{x\sim p}\left[\log \frac{P(x)}{Q(x)}\right] = E_{x\sim p}\left[\log P(x) - \log Q(x)\right]\\ & \qquad = \sum_{i = 1}^{N}\underline{\mathrm{P}(x_i)} (\underline{\log P(x_i)} -\underline{\log Q(x_i)}) \end{array}
$$

$$
l_{n} = y_{n}\cdot (\log y_{n} - x_{n})
$$

# 损失函数

Loss Function

10、nn.MarginRankingLoss

功能：计算两个向量之间的相似度，用于排序任务

特别说明：该方法计算两组数据之间的差异，返回一个n*n的loss矩阵主要参数：

margin：边界值，x1与x2之间的差异值reduction：计算模式，可为none/sum/mean

$y = 1$  时，希望x1比x2大，当x1>x2时，不产生loss $y = - 1$  时，希望x2比x1大，当x2>x1时，不产生loss

nn.MarginRankingLoss(margin=0.0, size_average=None, reduce=None, reduction='mean')

loss(x,y)=max(0,- y\*(x1- x2)+ margin)

# 损失函数

Loss Function

11、nn.MultiLabelMarginLoss

![](images/e2d404402287625f1daedc2a2a26bfca7179616a655cebd3768202176d8f98dd.jpg)

# 损失函数

Loss Function

12、nn.SoftMarginLoss

功能：计算二分类的logistic损失

主要参数：

reduction：计算模式，可为none/sum/mean

nn.SoftMarginLoss(size_average=None, reduce=None, reduction='mean')

log(1+exp(- y[i]\*x[i])) loss(x,y)= X.nelement() i

# 损失函数

Loss Function

# 13、nn.MultiLabelSoftMarginLoss

功能：SoftMarginLoss多标签版本

# 主要参数：

weight：各类别的loss设置权值reduction：计算模式，可为none/sum/mean

nn.MultiLabelSoftMarginLoss(weight=None, size_average=None, reduce=None, reduction='mean')

![](images/da4d3f0488d4c515bda1dcb404103e98344f76fdf6d9ae9ca82f82df4ef7dda4.jpg)

![](images/a553965a23d5f837fcd30e3ac01cc22ba4499a5c27cb62271ea6cbf8c2b14a8f.jpg)

# 损失函数

Loss Function

14、nn.MultiMarginLoss

功能：计算多分类的折页损失

# 主要参数：

p：可选1或2weight：各类别的loss设置权值margin：边界值reduction：计算模式，可为none/sum/mean

nn.MultiMarginLoss(p=1, margin=1.0, weight=None, size_average=None, reduce=None, reduction='mean')

loss(x,y)=∑max(0, margin - x[y] + x[i]))P x.size(0)

where  $x\in \{0,\dots ,x.\mathrm{size}(0) - 1\} ,y\in \{0,\dots ,y.\mathrm{size}(0) - 1\} ,0\leq y[j]\leq x.\mathrm{size}(0) - 1,\mathrm{and}$  y[j]for all i and j.

# 损失函数

Loss Function

15、nn.TripletMarginLoss

功能：计算三元组损失，人脸验证中常用

# 主要参数：

p：范数的阶，默认为2- margin：边界值- reduction：计算模式，可为none/sum/mean

![](images/1120ddf2f39663cc0d15d4d7c4bed2f63ddb429f2ec3ef1c6c663895386e705e.jpg)

nn.TripletMarginLoss(margin=1.0, p=2.0, eps=1e- 06, swap=False, size_average=None, reduce=None, reduction='mean')

$$
L(a,p,n) = \max \{d(a_i,p_i) - d(a_i,n_i) + \mathrm{margin},0\}
$$

$$
d(x_{i},y_{i}) = \| \mathbf{x}_{i} - \mathbf{y}_{i}\|_{p}
$$

# 损失函数

Loss Function

16、nn.HingeEmbeddingLoss

功能：计算两个输入的相似性，常用于非线性embedding和半监督学习

特别注意：输入x应为两个输入之差的绝对值

主要参数：

margin：边界值reduction：计算模式，可为none/sum/mean

nn.HingeEmbeddingLoss(margin=1.0, size_average=None, reduce=None, reduction='mean')

ifyn=1,

# 损失函数

Loss Function

17、nn.CosineEmbeddingLoss

功能：采用余弦相似度计算两个输入的相似性

# 主要参数：

margin：可取值[- 1，1]，推荐为[0,0.5]reduction：计算模式，可为none/sum/mean

nn.CosineEmbeddingLoss(margin=0.0,size_average=None,reduce=None,reduction='mean')

$\mathrm{loss}(x,y) = \left\{ \begin{array}{ll}1 - \cos (x_1,x_2), & \mathrm{if} y = 1\\ \max (0,\cos (x_1,x_2) - \mathrm{margin}), & \mathrm{if} y = - 1 \end{array} \right.$

$$
\cos (\theta) = \frac{A\cdot B}{\|A\|\|B\|} = \frac{\sum_{i = 1}^{n}A_{i}\times B_{i}}{\sqrt{\sum_{i = 1}^{n}(A_{i})^{2}}\times\sqrt{\sum_{i = 1}^{n}(B_{i})^{2}}}.
$$

# 损失函数

Loss Function

18、nn.CTCLoss

功能：计算CTC损失，解决时序类数据的分类 Connectionist Temporal Classification

# 主要参数：

- blank : blank label- zero_infinity : 无穷大的值或梯度置0- reduction : 计算模式，可为none/sum/mean

torch.nn.CTCLoss(blank=0, reduction='mean', zero_infinity=Falset)

# 参考文献：

A. Graves et al.: Connectionist Temporal Classification: Labelling Unsegmented Sequence Data with Recurrent Neural Networks

# 损失函数

Loss Function

1. nn.CrossEntropyLoss  
2. nn.NLLLoss  
3. nn.BCELoss  
4. nn.BCEWithLogitsLoss  
5. nn.L1Loss  
6. nn.MSELoss  
7. nn.SmoothL1Loss  
8. nn.PoissonNLLLoss  
9. nn.KLDivLoss

10. nn.MarginRankingLoss  
11. nn.MultiLabelMarginLoss  
12. nn.SoftMarginLoss  
13. nn.MultiLabelSoftMarginLoss  
14. nn.MultiMarginLoss  
15. nn.TripletMarginLoss  
16. nn.HingeEmbeddingLoss  
17. nn.CosineEmbeddingLoss  
18. nn.CTCLoss

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# 结语

在这次课程中，学习了14种损失函数

在下次课程中，我们将会学习

pytorch的优化器

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/42043233872135ccdda771a20e02f0592e20fcba57bf371dc759d2388e586837.jpg)  
公众号

![](images/858b140c369b375621e5f2da842b910710b403ec78de448fd69ebdf74b6e106c.jpg)  
客服微信