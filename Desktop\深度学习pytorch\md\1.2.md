# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/8df6e559c539b987b360760c8b40d8edc69feca06e854c0945e8dd89117aabb1.jpg)  
公众号

![](images/623a18e48de4a15b67cbf31452e8b8ba70e69d49cea17706a24167c65286c5b9.jpg)  
微信

# PyTorch简介与安装

导师：余老师

# 目录

PyTorch简介  Anaconda安装  Pycharm安装

# PyTorch简介

Introduction of PyTorch

# PyTorch简介

Introduction of PyTorch

2017年1月，FAIR（Facebook AI Research）发布PyTorch

PyTorch是在Torch基础上用python语言重新打造的一款深度学习框架

Torch是采用Lua语言为接口的机器学习框架，但因Lua语言较为小众，导致Torch知名度不高

![](images/9a029fc348b0006a84f2dbe8644aa1d062c15f4207bf570903b7e16253b5c64e.jpg)

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典论文

# PyTorch发展

Development of PyTorch

- 2017年1月正式发布PyTorch- 2018年4月更新0.4.0版，支持Windows系统，caffe2正式并入PyTorch- 2018年11月更新1.0稳定版，已GitHub增长第二快的开源项目- 2019年5月更新1.1.0版，支持TensorBoard，增强可视化功能- 2019年8月更新1.2.0版，更新torchvision，torchaudio和torchtext，增加更多功能

# PyTorch发展

Development of PyTorch

2014年10月至2018年02月arXiv论文中深度学习框架提及次数统计

PyTorch的增长速度与TensorFlow一致

![](images/97d4b7b2fafbdf021ea709705103096b551a3939355943958a0b3ba3dddb6235.jpg)

# PyTorch发展

Development of PyTorch

2019年3月各深度学习框架在GitHub上的Start, Forks, Watchers和Contributors数量对比

![](images/14c0467fbf959f6e8999d827bd5eb68193a43a5bd3ef8b8a0f7afa3f1c670581.jpg)

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典论文

# PyTorch优点

Advantage of PyTorch

- 上手快：掌握Numpy和基本深度学习概念即可上手

- 代码简洁灵活：用nn.module封装使网络搭建更方便；基于动态图机制，更灵活

- Debug方便：调试PyTorch就像调试Python代码一样简单

- 文档规范：https://pytorch.org/docs/可查各版本文档

- 资源多：arXiv中的新算法大多有PyTorch实现

- 开发者多：GitHub上贡献者(Contributors)已超过1100+

- 背靠大树：FaceBook维护开发

# 适合人群

Applicable Population

- 深度学习初学者：模型算法实现容易，加深深度学习概念认识

- 机器学习爱好者：数十行代码便可实现人脸识别，目标检测，图像生成等有趣实验

- 算法研究员：最新arXiv论文算法快速复现

![](images/96ff6203a5b3a5fa1067bacd260339d3ce88a512d6b2bf28dbde2f91010c0cac.jpg)

# 软件安装

Software Installation

软件安装 Software Installation

Python包管理器

Python集成开发环境

![](images/30808dd40174a66e3a015853898d372d13fde1f955f8f637e817f6b6b861509b.jpg)

![](images/57c115989acfaef05add80982603c51e4e47f16d12461c3a29d1a46810486ba3.jpg)

![](images/dcf038ffea886f03125634179a0d1b5c8ddcc5dbbba8137878431d188831797e.jpg)

PyTorch

# 解释器与工具包

Interpreter and Module

# 解释器

![](images/a8720506394fb5d685b5342b96b2599b337be934db1536f8defd677e24f4c2fb.jpg)

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典论文

# 解释器与工具包

Interpreter and Module

# 工具包

工具包又称为依赖包、模块、库、包

python之所以强大是因为拥有大量工具包

内置包：os、sys、glob、re、math等

第三方包：pytorch，tensorflow，numpy等

![](images/c580b6bb8028235f98b0ff718d29c12b37237435907ed4a3108b50ba09a44eeb.jpg)

# 虚拟环境 Virtual Environment

![](images/6d8882c9f17bc1ca45d9a46b7ffa78242ec3e25d4f22fb7a1e45c7b081d584e2.jpg)

各环境间相互独立，可随意切换

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典论文

# Anaconda安装

Anaconda Installation

# Anaconda安装

Anaconda Installation

Anaconda是为方便使用python而建立的一个软件包，其包含常用的250多个工具包，多版本python解释器和强大的虚拟环境管理工具，所以Anaconda得名python全家桶

Anaconda可以使安装、运行和升级环境变得更简单，因此推荐安装使用

![](images/5eba8f6aa4fabdad93be6f6fb4e73dbfefc659ba99450bb03682339b2244298a.jpg)

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典论文

# Anaconda安装

Anaconda Installation

# 安装步骤：

1. 官网下载安装包 https://www.anaconda.com/distribution/#download-section  
2. 运行Anaconda3-2019.07-Windows-x86_64.exe  
3. 选择路径，勾选Add Anaconda to the system PATH environment variable，等待安装完成  
4. 验证安装成功，打开cmd，输入conda，回车  
5. 添加中科大镜像

# Pycharm安装

Pycharm Installation

# Pycharm安装

Pycharm Installation

Pycharm——强大的python IDE，拥有调试、语法高亮、Project管理、代码跳转、智能提示、版本控制等功能

![](images/0d12776fb07abf062dc92ff9a36c81a3d34abe4078d1308524c59af3f1a266ae.jpg)

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典论文

# Pycharm安装

Pycharm Installation

# 安装步骤：

1. 官网下载安装包 https://www.jetbrains.com/pycharm/  
2. 运行pycharm-professional-2019.2.exe  
3. 选择路径，勾选Add launchers dir to the PATH，等待安装完成

# PyTorch安装

PyTorch Installation

# PyTorch安装

PyTorch Installation

# 安装步骤：

1. 检查是否有合适GPU，若有，需安装CUDA与CuDNN  
2. CUDA与CuDNN安装（非必须）  
3. 下载whl文件，登陆https://download.pytorch.org/whl/torch_stable.html 命名解释：

cu92/torch- 1.2.0%2Bcu92- cp37- cp37m- win_amd64. whl

![](images/f2fc712a3123b5c1489b9c39588d988577f1ca7cfac617d3be6872155f307d70.jpg)

# PyTorch安装

PyTorch Installation

安装步骤：

3. （接上）下载pytorch与torchvision的whl文件，进入相应虚拟环境，通过pip安装  
4. 在pycharm中创建hello pytorch项目，运行脚本，查看pytorch版本

# 结语

在这次课程中，介绍了PyTorch的优点和发展，并讲解了PyTorch开发环境配置

有了开发环境，在下次课程中，我们将会学习PyTorch中的

Tensor（张量）

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典论文

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/465faf0e96e4bd3e2720636034fa2069644634ee1774946e84fbfa371dc8437a.jpg)  
公众号

![](images/e09270e3dd6f2dc085e5938dc1b3c203081344af0e73b856fbfec05a763dfa21.jpg)  
客服微信