# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# 模型创建与nn.Module

导师：余老师

# 目录

1/网络模型创建步骤2/nn.Module属性

# 模型创建步骤

Steps of Creating Module

# 模型创建步骤

Steps of Creating Module

![](images/8660b11ac950c089bd7de30d7ec4f19881f3e24f6256ad58aafd89e1da2d9b12.jpg)

机器学习模型训练步骤关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# 模型创建步骤

Steps of Creating Module

![](images/0114d6fd67f7b9bc8f98bbacb7101ac07ada59d5cf05d33fee84f2e934614d9c.jpg)

# 模型创建步骤

Steps of Creating Module

![](images/14f3700d6806b5efbd63f8d602634048b2626b4624fadad17734674b755929c8.jpg)

# LeNet

Conv1 -→ pool1 -→ Conv2 -→ pool2 -→ fc1 -→ fc2 -→ fc3

关注公众号深度之眼，后台回复论文，获取50篇AI必读经典前沿论文

# 模型创建步骤

Steps of Creating Module

![](images/74502ead3dfcf723f395c4d7a287ef88026798d4661f1f0e78e7f2b4b952d4f0.jpg)

Conv1 pool1 Conv2 pool2 fc1 fc2 fc3关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# 模型创建步骤

Steps of Creating Module

![](images/922134d91ac1a688555732168d4dfed9e34b6281544aba2ef7f2b98e290b6fd9.jpg)

# nn.Module

nn.Module

# nn.Module

nn.Module

![](images/dcbbe35219b0cc2cb466824fdb55981c73e71bc1ee9a8eebe1c4ef5fc887967a.jpg)

# nn.Module

nn.Module

parameters: 存储管理nn.Parameter类- modules: 存储管理nn.Module类- buffers: 存储管理缓冲属性，如BN层中的running_mean- ***_hooks: 存储管理钩子函数

self._parameters  $=$  OrderedDict() self._buffers  $=$  OrderedDict() self._backward_hooks  $=$  OrderedDict() self._forward_hooks  $=$  OrderedDict() self._forward_pre_hooks  $=$  OrderedDict() self._state_dict_hooks  $=$  OrderedDict() self._load_state_dict_pre_hooks  $=$  OrderedDict() self._modules  $=$  OrderedDict()

# nn.Module

nn.Module

# nn.Module总结

- 一个module可以包含多个子module

- 一个module相当于一个运算，必须实现forward()函数

- 每个module都有8个字典管理它的属性

# 结语

在这次课程中，学习了nn.Module的概念以及

模型创建的两个要素

在下次课程中，我们将会学习

容器Containers以及AlexNet的搭建

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/aa31a07b8ac86ea02c030a07ddfe2d146dfeffcc19de56ce480d227fa7426aed.jpg)  
公众号

![](images/52645d6994d2fb88da2b28858948b8015daa7b9f266125b6b282d02d7730837b.jpg)  
客服微信