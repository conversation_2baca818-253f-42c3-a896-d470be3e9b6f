# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0f56d641fd5a878f9d008c22224ce47a581218d185918065da0dab359aec23a7.jpg)  
公众号

![](images/37ffdc46d37645bc4f09f244de059a5fcb5c788664c9bbacf5570763234ba806.jpg)

# 计算图与动态图机制

导师：余老师

# 目录

1/计算图  2/PyTorch的动态图机制

# 计算图

Computational Graph

# 计算图

Computational Graph

计算图是用来描述运算的有向无环图

计算图有两个主要元素：结点（Node）和边（Edge）

结点表示数据，如向量，矩阵，张量边表示运算，如加减乘除卷积等

用计算图表示：y=(x+w)\*（w+1)a=x+w b=w+1

![](images/add5ad740e705b3a72f63ed714db2173c6f6266a3dd23c6595ab54d4cc327a9d.jpg)

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# 计算图

Computational Graph

计算图与梯度求导 $y = (x + w)\times (w + 1)$ a=x+w b=w+1y=a\*b $\frac{\partial y}{\partial w} = \frac{\partial y}{\partial a}\frac{\partial a}{\partial w} +\frac{\partial y}{\partial b}\frac{\partial b}{\partial w}$ $= b*1 + a*1$ $= b + a$ $= (w + 1) + (x + w)$ $= 2*w + x + 1$ $= 2*1 + 2 + 1 = 5$

![](images/cff66cf700f9f89f69fd4af8ee8d36e802ad38cebcc7de31777af84707785be8.jpg)

# 计算图

Computational Graph

计算图与梯度求导 $y = (x + w) * (w + 1)$

叶子结点：用户创建的结点称为叶子结点，如X与W

is_leaf：指示张量是否为叶子结点

![](images/e4dc7771ec18c3f2b094e1a6871c0162d9b7cbb22fcee8b2a1c8209ea9b4d28a.jpg)

![](images/f4763b6a9b5961cd656fac1d12fe18451325a145e6ee20fce87a400c1aedcee0.jpg)

# 计算图

Computational Graph

grad_fn: 记录创建该张量时所用的方法（函数）  y.grad_fn = <MulBackward0>  a.grad_fn = <AddBackward0>  b.grad_fn = <AddBackward0>

![](images/61a5e82d584a4ecacdc84185ba517b61c43d494f77dd3f43449f76a6bbd36382.jpg)

![](images/23d49be8c8ec9838fc7d8b80019a9512658da3561dae282605802b5a43e8f601.jpg)

# 动态图

Dynamic Graph

# 动态图 vs 静态图

Dynamic VS Static Computational Graphs

![](images/a8e5f185577f8801ed79a03022e4cc7c4f3998b321759cf3a73754530b758b1b.jpg)

根据计算图搭建方式，可将计算图分为动态图和静态图

Back- propagation uses the dynamically created graph

W_h = torch.randn(20, 20, requires_grad=True)  W_x = torch.randn(20, 10, requires_grad=True)  x = torch.randn(1, 10)  prev_h = torch.randn(1, 20)

h2h = torch.mm(W_h, prev_h.t())  i2h = torch.mm(W_x, x.t())  next_h = h2h + i2h  next_h = next_h.tanh()

loss = next_h.sum()  loss.backward() # compute gradients!

![](images/95b21a89ec672048dc5942fa9d4320301039ff795534b76e632b4a9730f399b9.jpg)

# 动态图 PyTorch ↑

# 静态图 TensorFlow →

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

![](images/80b43ae7dbb3137b8668908854f31fb0587eb0ce40c8b5c5f4bab70d5ee6399e.jpg)

# 结语

在这次课程中，学习了计算图与动态图机制

在下次课程中，我们将会学习PyTorch的

自动求导系统torch.autograd及

逻辑回归实现

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/1ebc4f547bfc6c9507e27a89249206c7cd8ed326976cb9cca3786a60fc317a58.jpg)  
公众号

![](images/5c87da47d12047b72d7ac32c07019f4dff2e7e2beee13c2063e5f1641fa2502b.jpg)  
客服微信