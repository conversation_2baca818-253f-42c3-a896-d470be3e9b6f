# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# GPU的使用

导师：余老师

# 目录

![](images/be9344ae1715248371f563bb7f7d528b1d38463e1ffe1e0a50cea3f917286da3.jpg)

# GPU in PyTorch

GPU in PyTorch

CPU（Central Processing Unit，中央处理器）：主要包括控制器和运算器

GPU(Graphics Processing Unit，图形处理器)：处理统一的，无依赖的大规模数据运算

![](images/0f5ea5d70b0fda0bf2c003488fc081fa73f7b824b9c31a8b853219bdefdb0a4b.jpg)

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# GPU in PyTorch

GPU in PyTorch

# 数据迁移

![](images/3b81540c4e7df871ebf19b43ffbe21e1099055c6b635382140b6e290fff3e6b9.jpg)

![](images/5ddc940f99f1adf7eab09778294c6d2a302967ad2039771d6815851b873b7e38.jpg)

data: 1. Tensor 2. Module

![](images/6cdc0f9750c67edb54391c1ef47bd22c294f697e28094e69b9cb779a6d948bf3.jpg)

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# GPU in PyTorch

GPU in PyTorch

to函数：转换数据类型/设备

1. tensor.to(*args, **kwargs)  
2. module.to(*args, **kwargs)  区别：张量不执行inplace，模型执行inplace

x = torch.ones((3, 3))  x = x.to(torch.float(64)

x = torch.ones((3, 3))  x = x.to("cuda")

linear = nn.Linear(2, 2)  linear.to(torch.double)

gpu1 = torch.device("cuda")  linear.to(gpu1)

# GPU in PyTorch

GPU in PyTorch

# torch.cuda常用方法

torch.cuda常用方法1. torch.cuda.device_count(): 计算当前可见可用gpu数2. torch.cuda.get_device_name(): 获取gpu名称3. torch.cuda.manual_seed(): 为当前gpu设置随机种子4. torch.cuda.manual_seed_all(): 为所有可见可用gpu设置随机种子5. torch.cuda.set_device(): 设置主gpu为哪一个物理gpu（不推荐）推荐: os.environ.setdefault("CUDA_VISIBLE_DEVICES", "2, 3")

# GPU in PyTorch

GPU in PyTorch

torch.cuda常用方法

torch.cuda.set_device(): 设置主gpu为哪一个物理gpu（不推荐）推荐：os.environ.setdefault("CUDA_VISIBLE_DEVICES", "2,3")

![](images/fc858d19680bee011d0b862b49f985917555eb9dfd87b0549623b7751dac0d26.jpg)

# GPU in PyTorch

GPU in PyTorch

多gpu运算的分发并行机制

分发→并行运算→结果回收

![](images/bf2f24894658bb04ac5204563d0dbd51510e22914eda561c6d93b66ac75885e9.jpg)

![](images/1bfd4cd4a6653184f65649563659a2ce5c2b2f813f20db061d5e593a1277df1f.jpg)

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典回收结果

# GPU in PyTorch

GPU in PyTorch

# 多gpu运算的分发并行机制

torch.nn.DataParallel

功能：包装模型，实现分发并行机制

主要参数：

- module: 需要包装分发的模型- device_ids: 可分发的gpu，默认分发到所有可见可用gpu- output_device: 结果输出设备

# GPU in PyTorch

GPU in PyTorch

# 多gpu运算的分发并行机制

batch size in forward: 8  batch size in forward: 8  model outputs.size: torch.Size([16, 3])  CUDA_VISIBLE_DEVICES: 2,3  device_count: 2

gpu free memory: [10362, 10058, 9990, 9990]  batch size in forward: 4  batch size in forward: 4  batch size in forward: 4  batch size in forward: 4  model outputs.size: torch.Size([16, 3])  CUDA_VISIBLE_DEVICES: 0,1,3,2  device_count: 4

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# GPU in PyTorch

GPU in PyTorch

# 查询当前gpu内存剩余

```pythondef get_gpu_memory():    import os    os.system('nvidia- smi - q - d Memory | grep - A4 GPU | grep Free > tmp.txt')    memory_gpu = [int(x.split()[2]) for x in open('tmp.txt', 'r').readlines()]    os.system('rm tmp.txt')    return memory_gpu```

# GPU in PyTorch

GPU in PyTorch

# 查询当前gpu内存剩余

example:

gpu_memory = get_gpu_memory()  gpu_list = np.argsort(gpu_memory)[: - 1]  gpu_list_str = ''.join(map(str, gpu_list))  os.environ.setdefault("CUDA_VISIBLE_DEVICES", gpu_list_str)

print("\ngpu free memory:  $\{\}$  ".format(gpu_memory))  print("CUDA_VISIBLE_DEVICES :  $\{\}$  ".format(os.environ["CUDA_VISIBLE_DEVICES"])

>>> gpu free memory: [10362, 10058, 9990, 9990]  >>> CUDA_VISIBLE_DEVICES :0,1,3,2

# GPU in PyTorch

GPU in PyTorch

# gpu模型加载

报错1：

报错1:RuntimeError: Attempting to deserialize object on a CUDA device but torch.cuda.is_available() is False. If you are running on a CPU- only machine, please use torch.load with map_location=torch.device('cpu') to map your storages to the CPU.

解决：torch.load(path_state_dict, map_location="cpu")

# GPU in PyTorch

GPU in PyTorch

# gpu模型加载

报错2: RuntimeError: Error(s) in loading state_dict for FooNet:

Missing key(s) in state_dict: "linears.0. weight", "linears.1. weight", "linears.2. weight".

Unexpected key(s) in state_dict: "module.linears.0. weight", "module.linears.1. weight", "module.linears.2. weight".

解决：

from collections import OrderedDictnew_state_dict = OrderedDict()for k, v in state_dict_load.items():    namekey = k[7] if k.startswith('module.') else k    new_state_dict[namekey] = v

# 结语

在这次课程中，学习了PyTorch的GPU使用

在下次课程中，我们将会学习

PyTorch中常见问题

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/45fe37fe4564eb0d3a1580f87bec3f9fc42847dbbafea02f7749605e14ff3be1.jpg)  
公众号

![](images/8d19271a013d9f9a9ef9012ca817e6cfce6caa210a5b2b9edd177b907722b184.jpg)  
客服微信