# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/a23586cc8df9dd221759a51c0679366475ce25c774a069a76f86a4ac72dbb44a.jpg)  
公众号

![](images/69051d531c9714812c092770ea9619af15799027b8137e766ec763b4fd29362b.jpg)  
微信

# 生成对抗网络一瞥

导师：余老师

目录

![](images/deca9a47c3ded71eb61fc5714b32ea94334e03e8f7b8c1405ce3d6a3d3228c01.jpg)

# Generative Adversarial Nets

Generative Adversarial Nets

GAN：生成对抗网络——一种可以生成特定分布数据的模型

![](images/6cbb837df5dd2ff805893be8da2b966871a79f92e3f2f6b648df3e5a351d87d9.jpg)

![](images/f2c7af7491c312cd9429adb3514a786e9222123e6712a408b40f8d597ae497aa.jpg)

![](images/192ac584e57c270524e9142f75d536096823044d35085896f9097b86c20544e1.jpg)

Yann LeCun, Director of AI Research at Facebook and Professor at NYU  Written Jul 29 · Upvoted by Joaquín Quiñonero Candela, Director Applied Machine Learning at Facebook and Huang Xiao

Adversarial training is the coolest thing since sliced bread.

I've listed a bunch of relevant papers in a previous answer.

Expect more impressive results with this technique in the coming years.

![](images/93c821d571b8d4b7e5d63a15af6520d260fd19d7011fe86c6b9b822ebed3ba3c.jpg)

# Generative Adversarial Nets

Generative Adversarial Nets

# GAN网络结构

![](images/f8fb113c80bcf3e9453acd228dfc2f7c201db2dcc08530c4b56ab252105e0f70.jpg)  
FIGURE 1: The architecture of generative adversarial networks

![](images/552dd6b400cbe55fc2c843504dbf1cad608aea18778797f886b2e28c3d10f9b1.jpg)  
Figure 3: Generative Adversarial Network

![](images/583c217d07a08be5c83f0878552067325eafd418238ed61092875bfc7b7ecc96.jpg)

《Recent Progress on Generative Adversarial Networks (GANs): A Survey》  《How Generative Adversarial Networks and Its Variants Work: An Overview of GAN》  《Generative Adversarial Networks: A Survey and Taxonomy》 经典前沿论文

# Generative Adversarial Nets

Generative Adversarial Nets

GAN的训练

训练目的

训练目的1. 对于D：对真样本输出高概率2. 对于G：输出使D会给出高概率的数据

Algorithm 1 Minibatch stochastic gradient descent training of generative adversarial nets. The number of steps to apply to the discriminator,  $k$  is a hyperparameter. We used  $k = 1$  the least expensive option, in our experiments.

for number of training iterations do

for  $k$  steps do

Sample minibatch of  $m$  noise samples  $\{z^{(1)},\ldots ,z^{(m)}\}$  from noise prior  $p_g(z)$  Sample minibatch of  $m$  examples  $\{x^{(1)},\ldots ,x^{(m)}\}$  from data generating distribution  $p_{\mathrm{data}}(x)$  Update the discriminator by ascending its stochastic gradient:

$$
\nabla_{\theta_d}\frac{1}{m}\sum_{i = 1}^{m}\left[\log D\left(\pmb{x}^{(i)}\right) + \log \left(1 - D\left(G\left(\pmb{z}^{(i)}\right)\right)\right)\right].
$$

end for

Sample minibatch of  $m$  noise samples  $\{z^{(1)},\ldots ,z^{(m)}\}$  from noise prior  $p_g(z)$  Update the generator by descending its stochastic gradient:

$$
\nabla_{\theta_g}\frac{1}{m}\sum_{i = 1}^{m}\log \left(1 - D\left(G\left(z^{(i)}\right)\right)\right).
$$

end for

The gradient- based updates can use any standard gradient- based learning rule. We used momentum in our experiments.

# Generative Adversarial Nets

Generative Adversarial Nets

![](images/a982b6841827b47e04f293ea210ceb986e74e43cf830d90d8c5fdfb4eef7c2a1.jpg)

# Generative Adversarial Nets

Generative Adversarial Nets

GAN的训练

step1: 训练D输入：真实数据加G生成的假数据输出：二分类概率

step2: 训练G

输入：随机噪声z

输出：分类概率——D(G(z))

![](images/79f321f088cb441084adbd6039f49d63061d5bc0efda65bb7c7ccf027871ae1d.jpg)

![](images/86d1c647221ca8f60de7fdcc287631f03f2f93f17d75eb7ba9c4a176e9f4a594.jpg)

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# Generative Adversarial Nets

Generative Adversarial Nets

GAN的训练

训练目的

训练目的1. 对于D：对真样本输出高概率2. 对于G：输出使D会给出高概率的数据

Algorithm 1 Minibatch stochastic gradient descent training of generative adversarial nets. The number of steps to apply to the discriminator,  $k$  is a hyperparameter. We used  $k = 1$  the least expensive option, in our experiments.

for number of training iterations do

for  $k$  steps do

Sample minibatch of  $m$  noise samples  $\{z^{(1)},\ldots ,z^{(m)}\}$  from noise prior  $p_g(z)$  Sample minibatch of  $m$  examples  $\{x^{(1)},\ldots ,x^{(m)}\}$  from data generating distribution  $p_{\mathrm{data}}(x)$  Update the discriminator by ascending its stochastic gradient:

$$
\nabla_{\theta_d}\frac{1}{m}\sum_{i = 1}^{m}\left[\log D\left(\pmb{x}^{(i)}\right) + \log \left(1 - D\left(G\left(\pmb{z}^{(i)}\right)\right)\right)\right].
$$

end for

Sample minibatch of  $m$  noise samples  $\{z^{(1)},\ldots ,z^{(m)}\}$  from noise prior  $p_g(z)$  Update the generator by descending its stochastic gradient:

$$
\nabla_{\theta_g}\frac{1}{m}\sum_{i = 1}^{m}\log \left(1 - D\left(G\left(z^{(i)}\right)\right)\right).
$$

end for

The gradient- based updates can use any standard gradient- based learning rule. We used momentum in our experiments.

# Generative Ad

Generative Adversarial Nets

DCGAN

Discriminator: 卷积结构的模型

Generator: 卷积结构的模型

![](images/3e2a45fc6cde4f85986bdf9ce421e11290a996e78f62ebda021ba977b095785f.jpg)

# Generative Adversarial Nets

Generative Adversarial Nets

DCGAN

![](images/540d2578a18b9c53d96f03c4a73daa62ffb731c2e0eb273f0323fefb90e7fccc.jpg)  
《Unsupervised Representation Learning with Deep Convolutional Generative Adversarial Networks》典前沿论文

# Generative Adversarial Nets

Generative Adversarial Nets

# DCGAN 实现人脸生成

# Sample Images

![](images/6d585754147d0c682eda069bbbeae487bee87bcb2fd1d16e7ef658cd6429727b.jpg)

![](images/30af6913f75e58eda16ad23fbff824c8afe572f8bfe307b82142f804d3767b04.jpg)

# Generative Adversarial Net

Generative Adversarial Nets

# GAN的应用

GAN的应用https://medium.com/@jonathan_hui/gan- some- cool- applications- of- gans- 4c9ecca35900

![](images/2c1e43e192d62af3f5b1e60dc43dd707deafc0ff35044ab79cb0a7a925c6bedd.jpg)

# Generative Adv

Generative Adversarial Nets

GAN的应用：《CycleGAN》

![](images/f064d757411af4e2ff7a372104393c6b50771721cd72a1da933ede50a4c68402.jpg)

horse → zebra

# Generative Adv

Generative Adversarial Nets

![](images/708aee760fda5b4f63d5b8a37955cc5f0ef811a9e2be925ce92df019bddac7bf.jpg)

GAN的应用：《PixelDTGAN》

Example results on LOOKBOOK dataset(top), left is input, right is generated clothes. Results on a similar dataset (bottom). More results will be added soon.

![](images/b9b3ab019ccf5a86338ba8c7ee8a28ce73a9d4760a57d5fafd6fe5576ebe4547.jpg)

# Generative Adversarial Net

Generative Adversarial Nets

GAN的应用：《SRGAN》

![](images/3411adc57052c8f417bfa432c066a6642a7980474a76bf44173bf0e208a67edd.jpg)  
Figure 2: From left to right: bicubic interpolation, deep residual network optimized for MSE, deep residual generative adversarial network optimized for a loss more sensitive to human perception, original HR image. Corresponding PSNR and SSIM are shown in brackets. [4× upscaling]

关注公众号深度之眼，后台回SRGAN文，获取60篇AI必读经典前沿论文

# Generative Adversarial Net

Generative Adversarial

GAN的应用：

《Progressive GAN

![](images/2d88ff8ed229c5dd9398cc8e3c203e5e0872c0c88be7fc948f896ac26e2eb61c.jpg)  
Figure 5:  $1024 \times 1024$  images generated using the CELEBA-HQ dataset. See Appendix F for a larger set of results, and the accompanying video for latent space interpolations.

# Generative Adversarial Net

Generative Adversa

GAN的应用：

《StackGAN》

![](images/a4a40a7686ea91a5ad2559f53bffac6cac744f1dc7b53423fd453e21a3caae30.jpg)  
Figure 2. The architecture of the proposed StackGAN. The Stage-I generator draws a low resolution image by sketching rough shape and basic colors of the object from the given text and painting the background from a random noise vector. The Stage-II generator generates a high resolution image with photo-realistic details by conditioning on both the Stage-I result and the text again.

# Image inpainting

# Generative Ad

Generative Adversarial Nets

GAN的应用：

《Context Encoders》

Repair images have been an important subject decades ago. GAN is used to repair images and fill the missing part with created “content”.

![](images/b85e344e9198ae2f677e3a10445df32af652e5634aeafd81931f47f061053946.jpg)

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# Pix2Pix

Generative AdversariGAN的应用：《Pix2Pix》

Pix2Pix is an image- to- image translation that get quoted in cross- domainGAN's paper frequently. For example, it converts a satellite image into a map(the bottom left).

![](images/c815c7889e8da5812d4cdf572094f6d9638df54bea116f55fb6a4eae35c6b6b9.jpg)

Generati Generative Adversa GAN的应用：

《IcGAN》

# Image editing (IcGAN)

Reconstruct or edit images with specific attributes.

![](images/79689478b405187c50574e6c5b9afb16f54156c3dd0745d94009861b70330918.jpg)

# Generative Adversarial Net

Generative Adversarial Nets

# 结语

在这次课程中，学习了PyTorch中实现GAN

在下次课程中，我们将会学习

递归神经网络一瞥

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/28b270c8c82de7405856cfe9bf66fa424835bebd71568eb96328246baa443b5c.jpg)  
公众号

![](images/55d7a441b682c352f2170169c178383049091b512008d1697bb073bf92986e67.jpg)  
客服微信