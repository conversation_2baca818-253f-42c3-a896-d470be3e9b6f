# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/a23586cc8df9dd221759a51c0679366475ce25c774a069a76f86a4ac72dbb44a.jpg)  
公众号

![](images/69051d531c9714812c092770ea9619af15799027b8137e766ec763b4fd29362b.jpg)  
微信

# 图像目标检测一瞥

导师：余老师

目录

图像目标检测是什么？

模型是如何完成目标检测的？

深度学习目标检测模型简介

PyTorch中的Faster RCNN训练

# Object Detection

Object Detection

目标检测：判断图像中目标的位置

目标检测两要素

1. 分类：分类向量[p0, ..., pn]  
2. 回归：回归边界框[x1, y1, x2, y2]

![](images/571f161193fd040bce57010562573cd68600c405263213dcbbc9e817e60740f5.jpg)

# Object Detection

Object Detection

模型如何完成目标检测

将3D张量映射到两个张量

1. 分类张量：shape为 [N, c+1]  
2. 边界框张量：shape为 [N, 4]

边界框数量N如何确定？

![](images/1d4d67f3f8f97cb6a59ea8090a82127322061672245ab558eabe41caba3b619b.jpg)

# Object Detection

Object Detection

边界框数量N如何确定？

传统方法——滑动窗策略

缺点：

1. 重复计算量大  
2. 窗口大小难确定

![](images/9875836e9a4f4e116b8d3ec36dfa1b5d125b709f681838220e1067b2a92f01b1.jpg)

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# Object Detection

Object Detection

利用卷积减少重复计算

![](images/0590933df411a494edf345562d1b9f379c53db1ee6eebfdb1089d1f3337d0957.jpg)

# Object Detection

Object Detection

利用卷积减少重复计算

重要概念：

特征图一个像素对应原图一块区域

![](images/ef4c023a26f731c9173208867766e3e649668e38c24c6bf9fc6a1e60aa3248c5.jpg)  
图片来源：吴恩达《深度学习》

# Object Detection

Object Detection

# 目标检测模型简介

![](images/8b375dd1b8ad9418521a325751e2b45ffecaf9a58a498bf63c8342128e59d8ab.jpg)

# Object Detection

目标检测模型简介

目标检测模型简介

按流程分为：one- stage和two- stage

![](images/458cd12fc5be5cc1cdce09cf614de47f1aef26f7861e1974cfe0736f61c2516d.jpg)

# Object Detection

Object Detection目标检测模型简介

目标检测模型简介

two- stage

![](images/8739744666bdac598552a80dd3cbd12c928bd3b3c7d51e9c1a4aaf487ff0d6aa.jpg)

one- stage

# Object Detection

Object Detection

Faster RCNN——经典two stage检测网络

![](images/c4c2a82a5e1ae89de816b4793da065788fbb94bdb092ca2339922022cc89a18d.jpg)

![](images/8d501c64749adbf912e46b0e3a728ec841ce837153c435cb3980cd176b3d638f.jpg)

# Object Detection

Object Detection

# 2000→512

Faster RCNN——经典two stage检测网络

Faster RCNN 数据流

1. Feature map: [256, h_f, w_f]

2. 2 Softmax: [num anchors, h_f, w_f]

3. Regressors: [num anchors*4, h_f, w_f]

4. NMS OUT: [n_proposals=2000, 4]

5. ROI Layer: [512, 256, 7, 7]

6. FC1 FC2: [512, 1024]

7. c+1 Softmax: [512, c+1]

8. Regressors: [512, (c+1)*4]

![](images/00a27c9b9aac8ef46891daba42e79cdf77f92a2a2ac465aff2c3f18f9f2f288a.jpg)

# Object Detection

Object Detection

Faster RCNN——经典two stage检测网络

1. torchvision.models.detection.fasterrcnn_resnet50_fpn() 返回 FasterRCNN实例  
2. class FasterRCNN(GeneralizedRCNN)  
3. class GeneralizedRCNN(nn.Module) forward():

# Object Detection

Object Detection

Faster RCNN——经典two stage检测网络

1. features = self.backbone(images.tensors)

faster_rcnn.py 332行： backbone = resnet_fp_n_backbone('resnet50', pretrained_backbone)

backbone.utils.py 44行： backbone = resnet._dict_[backbone_name](pretrained=pretrained, norm_layer=misc.nn_ops.FrozenBatchNorm2d)

torchvision/models/resnet.py 195行 forward()

def forward（self,x): x=self.copy1（x) x=self.relu（x) x=self.maxpool（x) x=self.layerl（x) x=self.layer2（x) x=self.layer3（x) x=self.layer4（x) x=self.avgpool（x) x=torch.flatten（x,1) x=self.fc（x)

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# Object Detection

Object Detection

Faster RCNN——经典two stage检测网络

2. proposals, proposal losses = self.rpn(images, features, targets)

faster_rcnn.py 194行：rpn = RegionProposalNetwork(……)

torchvision/models/detection/rpn.py 380行：forward():

anchors = self-anchor_generator(images, features) proposals = self.box coder.decode(pred bbox deltas.detach(), anchors) proposals = proposals.view(num_images, - 1, 4) boxes, scores = self.filter proposals(proposals, objectness, images.image sizes, num anchors, per level) # 179046 - > 2000

![](images/4af79eebeb067ffe43e65b687d70faa06faea554c4af1bf11b33a8df9351b18e.jpg)

![](images/485d5a87ab1a6b4c2ac8cea3b24282f8a54b1cd874597a9acf6f1a72d01e01dd.jpg)

# Object Detection

Object Detection

Faster RCNN——经典two stage检测网络

3. detections, detector losses = self.roi heads(features, proposals, images.image_sizes, targets)

faster_rcnn.py 219行：roi heads = RoIHeads(……)

torchvision/models/detection/roi heads.py 522行：forward():if self.training:

box_features = self.box roi pool(features, proposals, image_shapes)box_features = self.box_head(box_features)class logits, box_regression = self.box_predictor(box_features)

![](images/541dad89a5264f961812681912eb1477a247a2bac4146c9f52bfa12e9ddd678b.jpg)

# Object Detection

Object Detection

Faster RCNN——经典two stage检测网络

Faster RCNN 主要组件

1. backbone  
2. rpn  
3. filter_proposals(NMS)  
4. roi_heads

![](images/eddb692e56b4a4de5d4b826d44fdf9894f6e6e55c2a4ac439efda65d5f2c5a38.jpg)

# Object Detection

Object Detection

Faster RCNN——行人检测

数据：PennFudanPed数据集，70张行人照片共345行人标签  官方地址：http://www.cis.upenn.edu/~jshi/ped.html/

模型：fasterrcnn_resnet50_fpn进行finetune

![](images/5fb8b849e713856a3eb8ec2930b4639dcc9000c82283d68fd3c35850968bf3d2.jpg)

![](images/1a482bb16aeee5bf16af2d9cf4b56dddd48953d77612676e2329242a6fe1d642.jpg)

# Object Detection

Object Detection

目标检测推荐github：https://github.com/amusi/awesome- object- detection

# 结语

在这次课程中，学习了PyTorch中图像目标检测概念与

Faster RCNN

在下次课程中，我们将会学习

对抗生成网络一瞥

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/683af8f34f98a713762af4a7ecccbe1cd9d3766e89f03069ab958dc51aa9dff7.jpg)  
公众号

![](images/5cc95f1a043eb1f7dbe65b3209ecceb831b328029335ca628d9f2bea63ed83a0.jpg)  
客服微信