# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/631625bccdb995e2872b14efafae9694b6e861a086d37a194246affe531f2917.jpg)  
公众号

![](images/f216fe5ba2ec484e1a85738e8f207a2159bcf07c89788f41111aa96e8239133a.jpg)  
微信

# transforms图像增强（一）

导师：余老师

# 目录

![](images/91dc566a082afad8c3fdb423db8fce3bbe41cec3ce55177c633970c637e8bf7a.jpg)

# 数据增强

Data Augmentation

# 数据增强

Data Augmentation

数据增强又称为数据增广，数据扩增，它是对训练集进行变换，使训练集更丰富，从而让模型更具泛化能力

![](images/5210b390ed52ca58f8b54f75594469e43ceab8c8eeb9dadb3ffac56e297eda32.jpg)

![](images/afd6da829e662b75095093cfeb986380e861d0cea1c4d35ca3a3ad87c23166c9.jpg)

![](images/d94e84c46459d25d5fd39d80724f2f7d71ea859f4705c0b594eb49a46265e710.jpg)

# 数据增强Data Augmentation

![](images/4f8e7e072a6c0d3f76dbfda7c6940751083f14708e5a391bbb8a8c26890735e4.jpg)  
数据增强

![](images/8fa9a0953a636425a833aa26c36ddaa96bf950975450dc6d0aa59010cb2759dd.jpg)

# transforms——裁剪

transforms——Crop

# transforms——Crop

transforms——Crop

# 1. transforms.CenterCrop

功能：从图像中心裁剪图片

size：所需裁剪图片尺寸

![](images/222b33b4af76eb01aebe3dc9fdeb028f4e5ca500d02baf612f45477b22dab199.jpg)  
关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# transforms——Crop

transforms——Crop

# 2. transforms.RandomCrop

功能：从图片中随机裁剪出尺寸为size的图片

size：所需裁剪图片尺寸

padding：设置填充大小

当为a时，上下左右均填充a个像素

当为(a，b)时，上下填充b个像素，左右填充a个像素

当为(a，b，c，d)时，左，上，右，下分别填充a，b，c，d

pad_if_need：若图像小于设定size，则填充

transformers.RandomCrop(size, padding=None, pad_if_needed=False, fill=0, padding_mode='constant')

# transforms——Crop

transforms——Crop

# 2. transforms.RandomCrop

功能：从图片中随机裁剪出尺寸为size的图片

padding_mode：填充模式，有4种模式

transforms.RandomCrop(size, padding=None, pad_if_needed=False, fill=0, padding_mode='constant')

1、constant：像素值由fill设定2、edge：像素值由图像边缘像素决定3、reflect：镜像填充，最后一个像素不镜像，eg：[1,2,3,4]→[3,2,1,2,3,4,3,2]4、symmetric：镜像填充，最后一个像素镜像，eg：[1,2,3,4]→[2,1,1,2,3,4,4,3]fill：constant时，设置填充的像素值

# transforms——Crop

transforms——Crop

# 3. RandomResizedCrop

功能：随机大小、长宽比裁剪图片

size：所需裁剪图片尺寸

scale：随机裁剪面积比例，默认(0.08，1)

ratio：随机长宽比，默认(3/4，4/3)

interpolation：插值方法PIL.Image.NEARESTPIL.Image.BILINEARPIL.Image.BICUBIC

RandomResizedCrop(size, scale=(0.08, 1.0), ratio=(3/4, 4/3), interpolation)

# transforms——Crop

transforms——Crop

4. FiveCrop

5. TenCrop

功能：在图像的上下左右以及中心裁剪出尺寸为size的5张图片，TenCrop对这5张图片进行水平或者垂直镜像获得10张图片

size：所需裁剪图片尺寸vertical_flip：是否垂直翻转

transforms.FiveCrop(size)

transforms.TenCrop(size, vertical_flip=False)

# transforms——翻转、旋转

transforms——Flip and Rotation

# transforms——Flip

transforms——Flip

1. RandomHorizontalFlip  
2. RandomVerticalFlip

功能：依概率水平（左右）或垂直（上下）

翻转图片

p：翻转概率

![](images/10ae768b566e18798a823a9ee8590d93f25bcdbd718a61b3382a11f8047dbac7.jpg)

# transforms——Rotation

transforms——Rotation

3. RandomRotation

功能：随机旋转图片

- degrees: 旋转角度  当为a时，在（-a，a）  当为(a，b)时，在(a，b)  - resample: 重采样方法  - expand: 是否扩大图片

![](images/44694541810deb93cf46d0059fa8dced11bc5298d87de597e3cfa59a70f34659.jpg)

RandomRotation(degrees, resample=False, expand=False, center=None)

![](images/9aa53a0deea697787adf7ee558348331c4e40abb872afe2223508c2d467036ff.jpg)

# transforms——Rotation

transforms——Rotation

3. RandomRotation

功能：随机旋转图片

RandomRotation(degrees, resample=False, expand=False, center=None)

degrees:旋转角度

![](images/328e900d65e9e22243524a6594d6db3e2180c8c888b01ec0463875821e32aa63.jpg)

![](images/5493b96ed7f76721549fd3baa19f9cbf2f3099356faac5f583d0423d594f203e.jpg)

# 结语

在这次课程中，学习了数据预处理transforms的数据增强方法——裁剪、翻转和旋转

在下次课程中，我们将会学习PyTorch的

transforms其他数据增强方法

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/1325f4fac2a7cc08fead6e762c0bd4501535d5bd3d7a13032631423ec28a2aa9.jpg)  
公众号

![](images/246966bb09491aade2f93edc91a08e23e9a3a79ce85900a1af04dfc8ff9c6805.jpg)  
客服微信