# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/a23586cc8df9dd221759a51c0679366475ce25c774a069a76f86a4ac72dbb44a.jpg)  
公众号

![](images/69051d531c9714812c092770ea9619af15799027b8137e766ec763b4fd29362b.jpg)  
微信

# 循环神经网络一瞥

导师：余老师

# 目录

循环神经网络 (RNN) 是什么？

RNN如处理成不定长输入？

训练RNN实现人名分类

# Recurrent Neural Networks

Recurrent Neural Networks

RNN: 循环神经网络

- 处理不定长输入的模型- 常用于NLP及时间序列任务（输入数据具有前后关系）

![](images/7a523049b0d6d884eed940a2141d450ed53fb910b20f5cb80f901ab203c9d4d4.jpg)

# Recurrent Neural Networks

Recurrent Neural Networks

# RNN网络结构

xt: 时刻t的输入，shape = (1, 57)  st: 时刻t的状态值，shape = (1, 128)  ot: 时刻t的输出值，shape = (1, 18)  U: linear层的权重参数，shape = (128, 57)  W: linear层的权重参数，shape = (128, 128)  V: linear层的权重参数，shape = (18, 128)

$$
s_{t} = f(Ux_{t} + Ws_{t - 1}),
$$

![](images/b165ec433e2ffeda26b121dc0e6f0071f24af570b2a8906c85b3d3499d40f93e.jpg)

hidden state: 隐藏层状态信息，记录过往时刻的信息

http://www.wldml.com/2015/09/recurrent- neural- networks- tutorial- part- 1- introduction- to- rnns/经典前沿论文

# Recurrent Neural Networks

Recurrent Neural Networks

RNN实现人名分类

问题定义：输入任意长度姓名（字符串），输出姓名来自哪一个国家（18分类任务）

数据：https://download.pytorch.org/tutorial/data.zip

Jackie Chan 成龙

Jay Chou 周杰伦

Tingsong Yue 余霆嵩

思考：

计算机如何实现不定长字符串到分类向量的映射？

![](images/1e5c30209e54c302719e4b9a3f029057079c6aedb775622864498b0899c9ae52.jpg)

![](images/8e755d6da88f5f5928951cbf22e90e0fdcb809151ac14a6d7a59f019fbdc646f.jpg)

![](images/98c308cf0e0ef14b8314d39b0db171898bb7c7633300e9100941e74ff3999108.jpg)

关注公众号深度之眼，后台回复论文，获取60篇AI经典前沿论文

# Recurrent Neural Networks

Recurrent Neural Networks

RNN实现人名分类

思考：计算机如何实现不定长字符串到分类向量的映射？

Chou（字符串）→RNN→Chinese（分类类别） 1. 单个字符→数字

2. 数字→model

3. 下一个字符→数字→model

4. 最后一个字符→数字→model→分类向量

for string in [C, h, o, u]:

1. one-hot: string→[0, 0, ..., 1, ..., 0]

2. y, h = model([0, 0, ..., 1, ..., 0], h)

![](images/d5d011c280788e69ea518500f417cc6a3cbcb5b9cece97ba12fb1ab0b5e76552.jpg)

关注公众号深度之眼，后台回复论文，获取50篇AI必读经典前沿论文

# 结语

在这次课程中，学习了PyTorch中实现RNN

本期课程到此结束 感谢大家的坚持学习

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/b86f815a44f04804c925a082570133df08eefd3a5ef1031b5ed22cb58a8b6894.jpg)  
公众号

![](images/5ecbeadb9b968bef233ea3b652fc067d9456b29da8f167b01276c16e1a5ff64d.jpg)  
客服微信