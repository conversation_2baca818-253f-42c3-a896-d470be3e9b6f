# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# 学习率调整策略

导师：余老师

# 目录

![](images/16393fad3ad21232412e6525ceefd968653a627047b62921c9063414f7aba5ec.jpg)

# 学习率调整

Adjust Learning Rate

梯度下降： $w_{i + 1} = w_{i} - \mathsf{LR} * g(w_{i})$ 学习率（learning rate）控制更新的步伐

![](images/4e370f001f6fddcc9ac7b661a0fd7cd16b06d02e49fc879072cda841748e6607.jpg)

![](images/3dd01e789886361cbac028222ab1745d47d9d66ccf57ddd717bf5371f7318cce.jpg)

# 学习率调整

Adjust Learning Rate

梯度下降：  $w_{i + 1} = w_{i} - g(w_{i})$ $w_{i + 1} = w_{i} - \mathsf{LR}*g(w_{i})$

学习率（learning rate）控制更新的步伐

![](images/09bc92462fce95f6a39ddb04a09a88fb6b5db9cfdf34c637c47531be3590a19c.jpg)

# 学习率调整

Adjust Learning Rate

class _LRScheduler

主要属性：

- optimizer: 关联的优化器- last_epoch: 记录epoch数- base_lrs: 记录初始学习率

class _LRScheduler(object):

def _init__(self, optimizer, last_epoch=- 1):

# 学习率调整

Adjust Learning Rate

class _LRScheduler

主要方法：

- step(): 更新下一个epoch的学习率- get_lr(): 虚函数，计算下一个epoch的学习率

class _LRScheduler(object):

def __init__(self, optimizer, last_epoch=- 1):

def get_lr(self):    raise NotImplementedError

# 学习率调整

Adjust Learning Rate

1. StepLR

功能：等间隔调整学习率

主要参数：

step_size：调整间隔数gamma：调整系数

调整方式：lr = lr * gamma

Ir_scheduler.StepLR(optimizer, step_size, gamma=0.1, last_epoch=- 1)

# 学习率调整

Adjust Learning Rate

2. MultiStepLR

功能：按给定间隔调整学习率

主要参数：

- milestones：设定调整时刻数- gamma：调整系数

调整方式：lr = lr * gamma

Ir_scheduler.MultiStepLR(optimizer,milestones,gamma  $= 0.1$  ,last_epoch=- 1)

# 学习率调整

Adjust Learning Rate

3. ExponentialLR

功能：按指数衰减调整学习率

主要参数：

gamma：指数的底

调整方式：lr = lr * gamma ** epoch

Ir_scheduler.ExponentialLR(optimizer, gamma, last_epoch=- 1)

# 学习率调整

Adjust Learning Rate

4. CosineAnnealingLR功能：余弦周期调整学习率主要参数：- T_max：下降周期- eta_min：学习率下限

Ir_scheduler.CosineAnnealingLR(optimizer, T_max, eta_min=0,last_epoch=- 1)

调整方式：  $\eta_t = \eta_{min} + \frac{1}{2} (\eta_{max} - \eta_{min})(1 + \cos (\frac{T_{cur}}{T_{max}}\pi))$

# 学习率调整

Adjust Learning Rate

# 5.ReduceLOnPlateau

功能：监控指标，当指标不再变化则调整主要参数：

mode：min/max两种模式

factor：调整系数

patience：“耐心”，接受几次不变化

cooldown：“冷却时间”，停止监控一段时间

verbose：是否打印日志

min_lr：学习率下限

eps：学习率衰减最小值

lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.1, patience=10, verbose=False, threshold=0.0001, threshold_mode='rel', cooldown=0, min_lr=0, eps=1e- 08)

# 学习率调整

Adjust Learning Rate

6. LambdaLR

功能：自定义调整策略主要参数：

- lr_lambda: function or list

Ir_scheduler.LambdaLR(optimizer, Ir_lambda, last_epoch=- 1)

# 学习率调整

Adjust Learning Rate

学习率调整小结

1. 有序调整：Step、MultiStep、Exponential 和 CosineAnnealing

2. 自适应调整：Reduce LROnPéateau

3. 自定义调整：Lambda

# 学习率调整

Adjust Learning Rate

# 学习率初始化：

1. 设置较小数：0.01、0.001、0.0001

2. 搜索最大学习率：《Cyclical Learning Rates for Training Neural Networks》

![](images/a6a5fab78c79742b7cb383e01abad7473a6213999192354d05aaadb135b1c445.jpg)

![](images/702ad7874d22f08a16a3e3eca53d79bee2f49370c17a5df7006ac45c02e37558.jpg)

# 结语

在这次课程中，学习了学习率调整策略

在下次课程中，我们将会学习

可视化方法——TensorBoard

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/ed7490774b08aafe797af128a819dfde349dd949f31a383abffb9a3bee59f664.jpg)  
公众号

![](images/8d19271a013d9f9a9ef9012ca817e6cfce6caa210a5b2b9edd177b907722b184.jpg)  
客服微信