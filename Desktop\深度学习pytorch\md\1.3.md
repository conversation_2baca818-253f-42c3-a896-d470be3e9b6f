# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/8df6e559c539b987b360760c8b40d8edc69feca06e854c0945e8dd89117aabb1.jpg)  
公众号

![](images/623a18e48de4a15b67cbf31452e8b8ba70e69d49cea17706a24167c65286c5b9.jpg)  
微信

# PyTorch开发环境安装

导师：余老师

# 目录

1 Anaconda安装  2 Pycharm安装  3 CUDA安装  4 PyTorch安装

# Anaconda安装

Anaconda Installation

# Anaconda安装

Anaconda Installation

# 安装步骤：

1. 官网下载安装包 https://www.anaconda.com/distribution/#download-section  
2. 运行Anaconda3-2019.07-Windows-x86_64.exe  
3. 选择路径，勾选Add Anaconda to the system PATH environment variable，等待安装完成  
4. 验证安装成功，打开cmd，输入conda，回车  
5. 添加中科大镜像源或者清华镜像源

# 虚拟环境

Virtual Environment

![](images/dbf554d831262a2f7b7b0e4a461940dea8c486cbdeee683af61dc7b83ff8b862.jpg)

各环境间相互独立，可随意切换

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典论文

# Pycharm安装

Pycharm Installation

# Pycharm安装

Pycharm Installation

# 安装步骤：

1. 官网下载安装包 https://www.jetbrains.com/pycharm/  
2. 运行pycharm-professional-2019.3.3.exe  
3. 选择路径，勾选Add launchers dir to the PATH，等待安装完成  激活步骤：

1. 参照：https://shimo.im/docs/GJTagHgh6kGYkKky/read（可直接搜：裸睡的猪）  
2. 新建项目，将破解文件拖入工作区，restart，重启完成

# CUDA安装

CUDA Installation

# CUDA安装

CUDA Installation

CUDA(Compute Unified Device Architecture)，是一种由NVIDIA推出的通用并行计算架构

cudnn：为深度学习计算设计的软件库

# 安装步骤：

安装步骤：1. 检查pytorch版本所支持的cuda版本，下载对应版本的CUDA2. 下载CUDA安装包，下载对应CUDA版本的cudnn安装包3. 安装并验证安装成功

# PyTorch安装

PyTorch Installation

# PyTorch安装

PyTorch Installation

# 安装步骤：

1. 检查是否有合适GPU，若有，需安装CUDA与CuDNN  
2. CUDA与CuDNN安装（非必须）  
3. 下载whl文件，登陆https://download.pytorch.org/whl/torch_stable.html 命名解释：

cu92/torch- 1.2.0%2Bcu92- cp37- cp37m- win_amd64. whl

![](images/0543b89e2a53c61d3e80849b4815aaa5b59b97fdc4741ad9e30b6d97efbc936d.jpg)

# PyTorch安装

PyTorch Installation

# 安装步骤：

3. 下载pytorch与torchvision的whl文件，进入相应虚拟环境，通过pip安装  
4. 在pycharm中创建hello pytorch项目，运行脚本，查看pytorch版本

# 虚拟环境

Virtual Environment

![](images/e56f421242d94126a74ed464b90182bf08566b2d0e1bc58c11599ad12c3b24d7.jpg)

各环境间相互独立，可随意切换

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典论文

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/6183d253f9ee3c605b7dc327831cad1d8e58af5a20d30d9f3a15fb5dd2a56641.jpg)  
公众号

![](images/7b20753362dbd46d69b6c6b4e0bb37530142e209f6a71c83572063f00c63aeb9.jpg)  
客服微信