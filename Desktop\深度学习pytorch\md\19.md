# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# TensorBoard使用（一）

导师：余老师

Accuracy

Accuracy

![](images/6c6dcd35afaff67794437399d7ac207342e342cd05146a387fa5d5bff1cc298c.jpg)

Loss

Loss

![](images/bd890eb5d493ea9e79fc7684718247b456a578325dfd673465845f28b7daac84.jpg)

conv1. weight_data runs/Oct25_22- 38- 12_tingsongdeMacBookPro.localtest_your_comment

![](images/613486d5bfcf035a9d06222f6c2f43ca8df35b8eb888c2e2e87ee10b844b6314.jpg)

conv1. weight_grad

conv1. weight_grad runs/Oct25_22- 38- 12_tingsongdeMacBookPro.localtest_your_comment

![](images/cce1ce7afe81ed0470b2b203db780af16a5c48af5647e69b2aca9f0975de3384.jpg)

关注公众号深度之眼，后台回复论文：获取60篇AI必读经典前沿论文

conv1. weight_data runs/Oct25_22- 38- 12_tingsongdeMacBookPro.localtest_your_comment

![](images/958f22e2e3083d90d0e6f0639320a75680f6405479200071678188ba9c329acb.jpg)

中

conv1. weight_grad

conv1. weight_grad runs/Oct25_22- 38- 12_tingsongdeMacBookPro.localtest_your_comment

![](images/d8932d5e2843938468989d71384df24c32ca39b896bef00444b73bb6b65c04cb.jpg)

# TensorBoard

TensorBoard

TensorBoard: TensorFlow中强大的可视化工具

![](images/6720f31fe78af6064a54793ea75f2c3806b4aca126044cf751316ff4e11e051e.jpg)

![](images/18131950e83eb4f0e3c385c761270783cba0363649e003c8d7ae4fd48d0781f2.jpg)

# TensorBoard

TensorBoard

SummaryWriter

功能：提供创建event file的高级接口主要属性：

log_dir: event file输出文件夹- comment: 不指定log_dir时，文件夹后缀- filename_suffix: event file文件名后缀

class SummaryWriter(object):

def __init__(self, log_dir=None, comment="", purge_step=None, max_queue=10, flush_secs=120, filename_suffix='' )

# TensorBoard

TensorBoard

SummaryWriter

1. add_scalar()

功能：记录标量

- tag：图像的标签名，图的唯一标识- scalar_value：要记录的标量- global_step：x轴2. add scalars()- main_tag：该图的标签

add_scalar(tag, scalar_value, global_step=None, walltime=None)

add scalars(main_tag, tag_scalar_dict, global_step=None, walltime=None)

# TensorBoard

TensorBoard

SummaryWriter

3. add_histogram()

功能：统计直方图与多分位数折线图

- tag: 图像的标签名，图的唯一标识- values: 要统计的参数- global_step: y轴- bins: 取直方图的bins

add_histogram(tag, values, global_step=None, bins='tensorflow', walltime=None)

# 结语

在这次课程中，学习了scalar与histogram以及

训练曲线，参数和梯度的监控

在下次课程中，我们将会学习

tensorboard的图像可视化方法

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/b86f815a44f04804c925a082570133df08eefd3a5ef1031b5ed22cb58a8b6894.jpg)  
公众号

![](images/5ecbeadb9b968bef233ea3b652fc067d9456b29da8f167b01276c16e1a5ff64d.jpg)  
客服微信