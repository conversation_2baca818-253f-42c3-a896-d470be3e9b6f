# PyTorch简介与安装

导师：余老师

# 目录

PyTorch简介  Anaconda安装  Pycharm安装

# PyTorch简介

Introduction of PyTorch

# PyTorch简介

Introduction of PyTorch

2017年1月，FAIR（Facebook AI Research）发布PyTorch

PyTorch是在Torch基础上用python语言重新打造的一款深度学习框架

Torch是采用Lua语言为接口的机器学习框架，但因Lua语言较为小众，导致Torch知名度不高

![](images/da8740961a31b0d635a1a222660da87b27b034ee4da5268efe8ebdf9bb35da1a.jpg)

Facebook AI Research

# PyTorch发展

Development of PyTorch

- 2017年1月正式发布PyTorch- 2018年4月更新0.4.0版，支持Windows系统，caffe2正式并入PyTorch- 2018年11月更新1.0稳定版，已GitHub增长第二快的开源项目- 2019年5月更新1.1.0版，支持TensorBoard，增强可视化功能- 2019年8月更新1.2.0版，更新torchvision，torchaudio和torchtext，增加更多功能

# PyTorch发展

Development of PyTorch

2014年10月至2018年02月arXiv论文中深度学习框架提及次数统计

PyTorch的增长速度与TensorFlow一致

![](images/fff480015ea20f07977129e079cd6533b9ebfa5725d38d4ad37b6c64cf3cfa2b.jpg)

# PyTorch发展

Development of PyTorch

2019年3月各深度学习框架在GitHub上的Start, Forks, Watchers和Contributors数量对比

![](images/dc48ea8c51958fe8be57a233067c7bc08c205ede2722b4a852c4c959118c21d5.jpg)

# PyTorch优点

Advantage of PyTorch

- 上手快：掌握Numpy和基本深度学习概念即可上手

- 代码简洁灵活：用nn.module封装使网络搭建更方便；基于动态图机制，更灵活

- Debug方便：调试PyTorch就像调试Python代码一样简单

- 文档规范：https://pytorch.org/docs/可查各版本文档

- 资源多：arXiv中的新算法大多有PyTorch实现

- 开发者多：GitHub上贡献者(Contributors)已超过1100+

- 背靠大树：FaceBook维护开发

# 适合人群

Applicable Population

- 深度学习初学者：模型算法实现容易，加深深度学习概念认识

- 机器学习爱好者：数十行代码便可实现人脸识别，目标检测，图像生成等有趣实验

- 算法研究员：最新arXiv论文算法快速复现

![](images/a8c7bc1abcc98f4abb5f4357e9ac8a8de537d8a1c49d6f7478b4bb1ecddab85d.jpg)

# 软件安装

Software Installation

软件安装 Software Installation

Python包管理器

Python集成开发环境

![](images/faf62e14174e3ca3247cbf8dd6ac59e863b5472a3f3581c56a03fac527c6b9fb.jpg)

![](images/65a974a5174a145b6f8d338a14635da094935fe2fa490d4bc9b9f0f40b0aaf09.jpg)

![](images/5ff3e0078ded75af3d250b29c96d3307f63bb9317a2682bbc2e47e4c2b572e97.jpg)

# 解释器与工具包

Interpreter and Module

# 解释器

# 将python语言翻译成机器指令语言

import os import numpy print "hello world"

![](images/8db1598d37659ba8bb61f4c5d0887a65569e476955131ef201415fb830d7de51.jpg)

python.exe （解释器）

![](images/bbdeb01cc9a8eba6f878a65d7d3de8a0583472f4869b3ec62e76f53a766de94f.jpg)

![](images/e5cc8bf1879cd5f007b5132fd43d0eb6d2e1a5f7c2e5b02563ebba53f45bed02.jpg)

python2与python3 解释器可能不兼容

![](images/8fa8743fb7cff5b72db59131713073b950cb930e7f45ca6fe47f94daabe8c90f.jpg)

![](images/fde2a0f4a2526b45eebd3dcb5dceb600b8e46eca0592ceaf944d063d34845efe.jpg)

# 解释器与工具包

Interpreter and Module

# 工具包

工具包又称为依赖包、模块、库、包

python之所以强大是因为拥有大量工具包

内置包：os、sys、glob、re、math等

第三方包：pytorch，tensorflow，numpy等

![](images/d83e28be8b7218f70aba9e7cbaa6e4141a343602f37a727b68ae0ec3bb16737e.jpg)

# 虚拟环境 Virtual Environment

![](images/8595393742430242f0b16fc223dc44bddf30180a6817687b1f13dbda661c5251.jpg)

各环境间相互独立，可随意切换

# Anaconda安装

Anaconda Installation

# Anaconda安装

Anaconda Installation

Anaconda是为方便使用python而建立的一个软件包，其包含常用的250多个工具包，多版本python解释器和强大的虚拟环境管理工具，所以Anaconda得名python全家桶

Anaconda可以使安装、运行和升级环境变得更简单，因此推荐安装使用

![](images/15a6adb0e01373a8354dffadd17a0bfa6bb74cab5cc8ceb5924e0123dff44795.jpg)

# Anaconda安装

Anaconda Installation

# 安装步骤：

1. 官网下载安装包 https://www.anaconda.com/distribution/#download-section  
2. 运行Anaconda3-2019.07-Windows-x86_64.exe  
3. 选择路径，勾选Add Anaconda to the system PATH environment variable，等待安装完成  
4. 验证安装成功，打开cmd，输入conda，回车  
5. 添加中科大镜像

# Pycharm安装

Pycharm Installation

# Pycharm安装

Pycharm Installation

Pycharm——强大的python IDE，拥有调试、语法高亮、Project管理、代码跳转、智能提示、版本控制等功能

![](images/066fe986567d34a4e48ac29691bdb6565f8bcb94776f967210f47f479abb9f9c.jpg)

# Pycharm安装

Pycharm Installation

# 安装步骤：

1. 官网下载安装包 https://www.jetbrains.com/pycharm/  
2. 运行pycharm-professional-2019.2.exe  
3. 选择路径，勾选Add launchers dir to the PATH，等待安装完成

# 激活步骤：

1. 下载破解文件:https://pan.baidu.com/s/112tS3XiAENiHaJ-aSCe0dA#list/path=%2F  
2. 将jetbrains-agent.jar放到pycharm安装目录中\bin文件夹  
3. 创建空项目，在pycharm64.exe.vmoptions中添加命令-javaagent::安装目录\jetbrains-agent.jar

4. 重启，完成激活

# PyTorch安装

PyTorch Installation

# PyTorch安装

PyTorch Installation

# 安装步骤：

1. 检查是否有合适GPU，若有，需安装CUDA与CuDNN2. CUDA与CuDNN安装（非必须）3. 下载whl文件，登陆https://download.pytorch.org/whl/torch_stable.html命名解释：cu92/torch-1.2.0%2Bcu92-cp37-cp37m-win_amd64.whlcuda版本或pytorch版本号python版本号 操作系统cpu

# PyTorch安装

PyTorch Installation

安装步骤：

3. （接上）下载pytorch与torchvision的whl文件，进入相应虚拟环境，通过pip安装  
4. 在pycharm中创建hello pytorch项目，运行脚本，查看pytorch版本

# 结语

在这次课程中，介绍了PyTorch的优点和发展，并讲解了PyTorch开发环境配置

有了开发环境，在下次课程中，我们将会学习PyTorch中的

Torchvision模块

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/71b7d2c435e7174de8f8b898fcf948943298bc2404322c29ac303c688b38693e.jpg)  
公众号

![](images/2a26de5b1516b49314965656e61f01ff4ada75fc02e2e4eb5cfdb935cd0d7f07.jpg)  
客服微信