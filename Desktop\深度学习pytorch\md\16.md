# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# 优化器 Optimizer (二)

导师：余老师

# 目录

learning rate 学习率

momentum 动量

torch.optim.SGD

Pytorch的十种优化器

# 学习率

Learning Rate

梯度下降：Wi+1=w- g(w)

y=f(x)=4\*x²

y' = f'(x) = 8\* x

xo=2,yo=16,f'(xo)=16

x1=xo- f'(xo)=2- 16=- 14

x1=- 14,y1=784,f'(x)=- 112

x2=x1- f'(x)=- 14+112=98,y2=38416

![](images/91bf6de63652f47567202b65f712c0851768e5e039e6da40f3fc9f9e774b02c8.jpg)

# 学习率

Learning Rate

梯度下降：  $w_{i + 1} = w_{i} - g(w_{i})$

![](images/f5483edc0cad4c1a4eb6ff1e42b46588029fd7cc155c3bf5249a38d8009c9fb9.jpg)

学习率（learning rate）控制更新的步伐

![](images/c26381a5df847174609cbe11b783f1f83b1f60648f3b6fcb9733428f7d71bf73.jpg)

# 优化器

Optimizer

Momentum（动量，冲量）：结合当前梯度与上一次更新信息，用于当前更新

![](images/272a3e21233a1a40c67e0adc7b9769e3a15a5807f485f1aeee65406c7e6faecf.jpg)

# 优化器

Optimizer

Momentum（动量，冲量）

指数加权平均：  $\mathrm{v}_t = \beta *v_{t - 1} + (1 - \beta)*\theta_t$ $\begin{array}{rl} & {\mathrm{v}_{100} = \beta *v_{99} + (1 - \beta)*\theta_{100}}\\ & {= (1 - \beta)*\theta_{100} + \beta *\left(\beta *v_{98} + (1 - \beta)*\theta_{99}\right)}\\ & {= (1 - \beta)*\theta_{100} + (1 - \beta)*\beta *\theta_{99} + (\beta^2 *v_{98})} \end{array}$  = (1- β)\*θ100+β\*（β\*v98+(1- β)\*θ99) = (1- β)\*θ100+（1- β)\*β\*θ99+(β²\*v98) = (1- β)\*θ100+（1- β)\*β\*θ99+(1- β)\*β²\*θ98 + (β3\*v97) = (1- β)\*θ100+（1- β)\*β\*θ99+(1- β)\*β²\*θ98 + (β3\*v97) = ∑(1- β)\*β\* θN- i

![](images/dc4591a778a8b6cdc73030acdfe792f144385ba0cf20ea9abc65340188b96279.jpg)

# 优化器

Optimizer

Momentum（动量，冲量）

梯度下降：

$$
w_{i + 1} = w_{i} - lr * g(w_{i})
$$

pytorch中更新公式：

$$
\begin{array}{l}v_{i} = m * v_{i - 1} + g(w_{i}) \\ w_{i + 1} = w_{i} - lr * v_{i} \end{array}
$$

Wi+1：第i+1次更新的参数lr：学习率Vi：更新量m：momentum系数g(wi)：Wi的梯度

$$
\begin{array}{rl} & v_{100} = m * v_{99} + g(w_{100}) \\ & \qquad = g(w_{100}) + m * (m * v_{98} + g(w_{99})) \\ & \qquad = g(w_{100}) + m * g(w_{99}) + m^2 *v_{98} \\ & \qquad = g(w_{100}) + m * g(w_{99}) + m^2 *g(w_{98}) + m^3 *v_{97} \end{array}
$$

关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# 优化器

Optimizer

1. optim.SGD

主要参数：

params：管理的参数组

lr：初始学习率

momentum：动量系数，贝塔

weight_decay：L2正则化系数

nesterov：是否采用NAG

optim.SGD(params, lr=<object object>, momentum=0, dampening=0, weight_decay=0, nesterov=False)

# 优化器

Optimizer

1. optim.SGD: 随机梯度下降法  
2. optim.Adagrad: 自适应学习率梯度下降法  
3. optim.RMSprop: Adagrad的改进  
4. optim.Adadelta: Adagrad的改进  
5. optim.Adam: RMSprop结合Momentum  
6. optim.Adamax: Adam增加学习率上限  
7. optim.SparseAdam: 稀疏版的Adam  
8. optim.ASGD: 随机平均梯度下降  
9. optim.Rprop: 弹性反向传播  
10. optim.LBFGS: BFGS的改进

# 优化器

1. optim.SGD: 《On the importance of initialization and momentum in deep learning》  
2. optim.Adagrad: 《Adaptive Subgradient Methods for Online Learning and Stochastic Optimization》

3. optim.RMSprop:  http://www.cs.toronto.edu/~tijmen/csc321/slides/lecture_slides_lec6.pdf

4. optim.Adadelta: 《AN ADAPTIVE LEARNING RATE METHOD》

5. optim.Adam: 《Adam: A Method for Stochastic Optimization》

6. optim.Adamax: 《Adam: A Method for Stochastic Optimization》

7. optim.SparseAdam

8. optim.ASGD: 《Accelerating Stochastic Gradient Descent using Predictive Variance Reduction》

9. optim.Rprop: 《Martin Riedmiller und Heinrich Braun》

10. optim.LBFGS: BDGS的改进  关注公众号深度之眼，后台回复论文，获取50篇AI必读经典前沿论文

# 结语

在这次课程中，学习了优化器Optimizer

在下次课程中，我们将会学习

学习率调整策略

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/4a7687106c2c2191364aaced4c8cb020fbeab35ba9b828380d6863554de641de.jpg)  
公众号

![](images/00d053dabbf3db0bc4aebab266b71d98b95c390888134b6ff16e8bc84153dd9b.jpg)  
客服微信