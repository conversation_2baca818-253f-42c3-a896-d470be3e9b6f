# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# 可视化工具——TensorBoard

导师：余老师

# 目录

![](images/09ed8bdf39dc14e6f742913b00ae67cf5856b8f0da06f90411a40f61b51a654c.jpg)

# TensorBoard

TensorBoard

![](images/a29a6b1e8a0eb60c0299b1c39d254a4ef7354ce8f502ffa6e76a9cd261432bfe.jpg)

机器学习模型训练步骤关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# TensorBoard

TensorBoard

![](images/4ef22c3a71f3ee271dcf83e5461c62f0e5542400ad69220b6afa0a3a1762445b.jpg)

# 关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# TensorBoard

TensorBoard

TensorBoard: TensorFlow中强大的可视化工具

![](images/6801223f54ac07494b12db708884ef4c8df3174c99ec5544c21edd42fbb03483.jpg)

![](images/717ac7ab66a5371e8f64d404ae4499d3fe862911884d8b017a679d14de5bb0ec.jpg)

# TensorBoard

TensorBoard

# 安装注意事项

pip install tensorboard的时候会报错：

ModuleNotFoundError: No module named 'past'

通过pip install future解决

# 结语

在这次课程中，学习了TensorBoard的概念与安装

在下次课程中，我们将会学习

pytorch中tensorboard的可视化方法

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/b86f815a44f04804c925a082570133df08eefd3a5ef1031b5ed22cb58a8b6894.jpg)  
公众号

![](images/5ecbeadb9b968bef233ea3b652fc067d9456b29da8f167b01276c16e1a5ff64d.jpg)  
客服微信