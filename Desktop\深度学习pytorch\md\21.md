# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# Hook函数与CAM算法

导师：余老师

# 目录

![](images/af759a45eb62f0c35e4f807e1225e13f611257c8899272059b60088790bcaa59.jpg)

# Hook Function

Hook Function

Hook函数机制：不改变主体，实现额外功能，像一个挂件，挂钩，hook

1. torch.Tensor.register_hook(hook)  
2. torch.nn.Module.register_forward_hook  
3. torch.nn.Module.register_forward_pre_hook  
4. torch.nn.Module.register_backward_hook

# Hook Function

Hook Function

1. Tensor.register_hook

功能：注册一个反向传播hook函数

Hook函数仅一个输入参数，为张量的梯度

hook(grad) - > Tensor or None

# Hook Function

Hook Function

计算图与梯度求导 $y = (x + w)\times (w + 1)$ a=x+w b=w+1y=a\*b $\frac{\partial y}{\partial w} = \frac{\partial y}{\partial a}\frac{\partial a}{\partial w} +\frac{\partial y}{\partial b}\frac{\partial b}{\partial w}$ $= b*1 + a*1$ $= b + a$ $= (w + 1) + (x + w)$ $= 2*w + x + 1$ $= 2*1 + 2 + 1 = 5$

![](images/eb1e0919b4ddd65ca64cb2f5f2829ee5ee853309865a65e659f49f984b3ee249.jpg)

# Hook Function

Hook Function

2. Module.register_forward_hook功能：注册module的前向传播hook函数参数：

- module: 当前网络层- input: 当前网络层输入数据- output: 当前网络层输出数据

hook(module, input, output) - > None

![](images/5c30d570f2b3088ff72658a89772997983d539acf445c00b7db648f4311730f9.jpg)

![](images/04d9783005bc287ec09c5076f457ae08f39bfadccd3b578dd98e1a3c1a477891.jpg)

# Hook Function

Hook Function

3. Module.register_forward_pre_hook功能：注册module前向传播前的hook函数参数：

- module: 当前网络层- input: 当前网络层输入数据

hook(module, input) - > None

# Hook Function

Hook Function

4. Module.register_backward_hook功能：注册module反向传播的hook函数参数：

- module: 当前网络层- grad_input: 当前网络层输入梯度数据- grad_output: 当前网络层输出梯度数据

hook(module, grad_input, grad_output) - > Tensor or None

# CAM and Grad-CAM

CAM and Grad- CAM

# CAM: 类激活图，class activation map

![](images/c5f3ac03c3809227cd86723b6103d496947bdf8fe6b99486954cd1e70ee42292.jpg)

# CAM and Grad-CAM

CAM and Grad- CAM

# Grad-CAM: CAM改进版，利用梯度作为特征图权重

![](images/8ece05c01209cdf89c5553a7a3b6f8606aa3f3a4298ddb91b35d07c0f3b5a321.jpg)

# CAM and Grad-CAM

CAM and Grad- CAM

![](images/f0975bacd953c6541393cdc1fd3f4556230c12c1c8afd46583cbc9681d47d0c1.jpg)

# 结语

在这次课程中，学习了hook函数与CAM可视化算法

在下次课程中，我们将会学习

L1与L2正则化

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/82ce1ed576ec28f667277433cefe41fca3e6c558f4d71e74f8d47ecb7f0e3572.jpg)  
公众号

![](images/495e7462c1e0d49f1a3a10490afd76e88225c845e3ad9fd8011556a717bcddec.jpg)  
客服微信