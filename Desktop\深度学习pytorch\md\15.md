# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# 优化器 Optimizer

导师：余老师

1/什么是优化器目录 2/optimizer的属性3/optimizer的方法

# 优化器

Optimizer

![](images/382e14d847ea40d39e655eb733cac79ee276a85fe8367b6d64c5f2395c92ec39.jpg)

机器学习模型训练步骤关注公众号深度之眼，后台回复论文，获取60篇AI必读经典前沿论文

# 优化器

Optimizer

pytorch的优化器：管理并更新模型中可学习参数的值，使得模型输出更接近

真实标签

导数：函数在指定坐标轴上的变化率  方向导数：指定方向上的变化率  梯度：一个向量，方向为方向导数  取得最大值的方向

![](images/576d2947f317fe88bc54a6bd0dd938b7941b0fbc09a4a4f7fbd5dbab6cceed5d.jpg)

# 优化器

Optimizer

# 基本属性

- defaults: 优化器超参数- state: 参数的缓存，如momentum的缓存- params_groups: 管理的参数组- _step_count: 记录更新次数，学习率调整中使用

class Optimizer(object): def_init_(self,params,defaults): self.defaults  $=$  defaults self.state  $=$  defaultdict(dict) self.param_groups  $=$

param_groups  $=$  {{'params': param_groups}

# 优化器

Optimizer

# 基本方法

zero_grad(): 清空所管理参数的梯度

pytorch特性：张量梯度不自动清零

class Optimizer(object):

def zero_grad(self):    for group in self.params:        for p in group['params']:            if p.grad is not None:                p.grad.detach_()                p.grad.zero_()

# 优化器

Optimizer

# 基本方法

- zero_grad(): 清空所管理参数的梯度- step(): 执行一步更新

class Optimizer(object):    def __init__(self, params, defaults):        self.defaults = defaults        self.state = defaultdict(dict)        self.param_groups = []

# 优化器

Optimizer

# 基本方法

- zero_grad(): 清空所管理参数的梯度- step(): 执行一步更新- add_param_group(): 添加参数组

class Optimizer(object):    def add_param_group(self, param_group):        for group in self.param_groups:            param_set.update(set(group['params']))    self.param_groups.append(param_group)

# 优化器

Optimizer

# 基本方法

- zero_grad(): 清空所管理参数的梯度- step(): 执行一步更新- add_param_group(): 添加参数组- state_dict(): 获取优化器当前状态信息字典- load_state_dict(): 加载状态信息字典

class Optimizer(object):

def state_dict(self):    return {        'state': packed_state,        'param_groups': param_groups,    }

def load_state_dict(self, state_dict):

# 结语

在这次课程中，学习了优化器Optimizer

在下次课程中，我们将会学习

常用的优化方法（优化器）

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/9737ae8cc2571cac4be4d186a1cf8013659166467deaefa4f991234978a142ac.jpg)  
公众号

![](images/1b7246dae4538c78967bef904d039362407596a436df73eb092accf8f5d5bdc5.jpg)  
客服微信