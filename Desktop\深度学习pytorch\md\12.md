# 法律声明

本课件包括演示文稿、示例、代码、题库、视频和声音等内容，深度之眼和讲师拥有完全知识产权；只限于善意学习者在本课程使用，不得在课程范围外向任何第三方散播。任何其他人或者机构不得盗版、复制、仿造其中的创意和内容，我们保留一切通过法律手段追究违反者的权利。

# 课程详情请咨询

- 微信公众号：深度之眼- 客服微信号：deepshare0920

![](images/0bae075d2faaa063221f768251e5944f29279f8076c5820c0847ebb02d884076.jpg)  
公众号

![](images/0887ac19aaa6a2576f36a242c302cd6aca7922bdd603755613e32ee3e9c0d6e4.jpg)  
微信

# 权值初始化

导师：余老师

# 目录

1 梯度消失与爆炸  2 Xavier方法与Kaiming方法  3 常用初始化方法

# 梯度消失与爆炸

Gradient Vanishing and Exploding

# 梯度消失与爆炸

Gradient Vanishing and Exploding

H2=H1\*W2 $\begin{array}{r}\Delta \mathrm{W}_2 = \frac{\partial\mathrm{Loss}}{\partial\mathrm{W}_2} = \frac{\partial\mathrm{Loss}}{\partial\mathrm{out}} *\frac{\partial\mathrm{out}}{\partial\mathrm{H}_2} *\frac{\partial\mathrm{H}_2}{\partial\mathrm{w}_2}\\ = \frac{\partial\mathrm{Loss}}{\partial\mathrm{out}} *\frac{\partial\mathrm{out}}{\partial\mathrm{H}_2} *\mathrm{H}_1 \end{array}$

梯度消失：  $\mathrm{H}_1\rightarrow 0\Rightarrow \Delta \mathrm{W}_2\rightarrow 0$  梯度爆炸：  $\mathrm{H}_1\rightarrow \infty \Rightarrow \Delta \mathrm{W}_2\rightarrow \infty$

![](images/042b3b1b1284bb9f4316fc82a8a5f6071aedc4693d08c76f337f8caa7b20e210.jpg)

# 梯度消失与爆炸

Gradient Vanishing and Exploding

1.  $\operatorname {E}(X*Y) = E(X)*E(Y)$  
2.  $\operatorname {D}(X) = E\big(\mathrm{X}^2\big) - [E(X)]^2$  
3.  $\operatorname {D}(X + Y) = D(X) + D(Y)$  
1.2.3 →D(X\*Y) = D(X)\*D(Y) + D(X)\* [E(Y)]² + D(Y)\* [E(X)]² 若E(X)=0, E(Y)=0 D(X\*Y) = D(X)\*D(Y)

# 梯度消失与爆炸

Gradient Vanishing and Exploding

$\begin{array}{rl} & {\mathrm{H}_{11} = \sum_{i = 0}^{n}X_{i}*W_{1i}\qquad \mathsf{D}(\mathsf{X}*\mathsf{Y}) = \mathsf{D}(\mathsf{X})*\mathsf{D}(\mathsf{Y})}\\ & {\mathsf{D}(\mathrm{H}_{11}) = \sum_{i = 0}^{n}D(X_{i})*D(W_{1i})}\\ & {\qquad = \mathsf{n}*(1*1)}\\ & {\qquad = \mathsf{n}} \end{array}$  std  $\begin{array}{r}\mathrm{d}(\mathrm{H}_{11}) = \sqrt{\mathbf{D}(\mathrm{H}_{11})} = \sqrt{n} \end{array}$ $\mathbf{D}(\mathrm{H}_1) = n*D(X)*D(W) = 1$

![](images/5cb53c7b3499146a17d9bbbba5e4d11c1b7ff4ff1aea6cce107704c7a80d38b5.jpg)

$\begin{array}{r}D(W) = \frac{1}{n}\Rightarrow \mathsf{std}(W) = \sqrt{\frac{1}{n}} \end{array}$

# Xavier初始化

Xavier Initialization

方差一致性：保持数据尺度维持在恰当范围，通常方差为1激活函数：饱和函数，如Sigmoid，Tanh

$\begin{array}{l}{n_i*D(W) = 1}\\ {n_{i + 1}*D(W) = 1}\\ {\Rightarrow D(W) = \frac{2}{n_i + n_{i + 1}}} \end{array}$  W\~U[- a, a]D(W)=  $\begin{array}{r}D(W) = \frac{(- a - a)^2}{12} = \frac{(2a)^2}{12} = \frac{a^2}{3}\\ \frac{2}{n_i + n_{i + 1}} = \frac{a^2}{3}\Rightarrow a = \frac{\sqrt{6}}{\sqrt{n_i + n_{i + 1}}} \end{array}$

参考文献：《Understanding the difficulty of training deep feedforward neural networks》经典前沿论文

# Kaiming初始化

Kaiming Initialization

方差一致性：保持数据尺度维持在恰当范围，通常方差为1

激活函数：ReLU及其变种

$\begin{array}{r}\mathsf{D}(W) = \frac{2}{n_i}\\ \mathsf{D}(W) = \frac{2}{(1 + \mathsf{a}^2)*n_i}\\ \mathsf{std}(W) = \sqrt{\frac{2}{(1 + \mathsf{a}^2)*n_i}} \end{array}$

参考文献：《Delving deep into rectifiers: Surpassing human- level performance on ImageNet classification》

# 十种初始化方法

Initialization Methods

1. Xavier均匀分布  
2. Xavier正态分布  
3. Kaiming均匀分布  
4. Kaiming正态分布  
5. 均匀分布  
6. 正态分布  
7. 常数分布  
8. 正交矩阵初始化  
9. 单位矩阵初始化  
10. 稀疏矩阵初始化

nn.init.calculate_gain(nonlinearity, param=None)

nn.init.calculate_gain

主要功能：计算激活函数的方差变化尺度

主要参数

nonlinearity: 激活函数名称

param: 激活函数的参数，如Leaky ReLU的negative_slop

https://pytorch.org/docs/stable/nn.init.html

# 结语

在这次课程中，学习了权值初始化方法的准则——方差一致性原则，以及Xavier和Kaiming

权值初始化方法在下次课程中，我们将会学习损失函数

deepshare.net

深度之眼

联系我们：  电话：18001992849  邮箱：<EMAIL>  Q Q: 2677693114

![](images/9737ae8cc2571cac4be4d186a1cf8013659166467deaefa4f991234978a142ac.jpg)  
公众号

![](images/1b7246dae4538c78967bef904d039362407596a436df73eb092accf8f5d5bdc5.jpg)  
客服微信